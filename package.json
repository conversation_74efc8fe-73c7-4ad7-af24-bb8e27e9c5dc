{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build": "vite build --mode production"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@originjs/vite-plugin-commonjs": "^1.0.3", "axios": "^1.9.0", "element-plus": "^2.9.8", "json-editor-vue3": "^1.1.1", "lodash": "^4.17.21", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "3.0.2", "vue": "^3.5.13", "vue-i18n": "12.0.0-alpha.2", "vue-router": "^4.5.0"}, "devDependencies": {"@iconify/json": "^2.2.331", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "sass": "^1.87.0", "typescript": "~5.8.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-mock": "^3.0.2", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}