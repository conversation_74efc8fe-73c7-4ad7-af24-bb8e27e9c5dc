# AI审方系统 - 登录功能说明

## 功能概述

已为AI审方系统添加了完整的登录功能，包括：

1. **登录界面** (`/login`)
2. **用户认证系统**
3. **路由守卫**
4. **状态管理**

## 新增文件

### 1. 登录页面
- `src/views/Login.vue` - 登录界面组件

### 2. API模块
- `src/api/module/authApi.ts` - 认证相关API接口

### 3. 状态管理
- `src/stores/modules/auth.ts` - 用户认证状态管理

## 功能特性

### 登录界面特性
- ✅ 用户名输入（3-20个字符）
- ✅ 密码输入（6-20个字符，支持显示/隐藏）
- ✅ 验证码输入（4位随机字符，点击刷新）
- ✅ 表单验证
- ✅ 响应式设计
- ✅ 加载状态显示

### 安全特性
- ✅ 路由守卫保护
- ✅ Token管理
- ✅ 登录状态持久化
- ✅ 自动登出功能

## 路由配置

```javascript
// 新增路由
/login          - 登录页面
/home           - 首页（需要登录）
/detail/:id     - 详情页（需要登录）
/progress/:id   - 进度页（需要登录）

// 默认重定向
/               - 重定向到 /login
```

## 使用方法

### 1. 访问系统
- 打开浏览器访问系统
- 自动重定向到登录页面 `/login`

### 2. 登录
- 输入用户名（测试可用任意3-20字符）
- 输入密码（测试可用任意6-20字符）
- 输入验证码（点击验证码可刷新）
- 点击"登录"按钮

### 3. 登录后
- 自动跳转到首页 `/home`
- 顶部显示用户欢迎信息
- 可点击"退出登录"按钮登出

## API接口

### 登录接口
```typescript
POST /v0-1/auth/login
{
  username: string,
  password: string,
  captcha: string
}
```

### 登出接口
```typescript
POST /v0-1/auth/logout
```

### 获取用户信息
```typescript
GET /v0-1/auth/userinfo
```

## 状态管理

使用Pinia进行状态管理，包括：

- `token` - 用户令牌
- `userInfo` - 用户信息
- `isLoggedIn` - 登录状态
- `login()` - 登录方法
- `logout()` - 登出方法

## 开发说明

### 1. 修改API地址
在 `src/api/module/authApi.ts` 中修改API接口地址

### 2. 自定义验证规则
在 `src/views/Login.vue` 中修改表单验证规则

### 3. 修改路由守卫
在 `src/router/index.ts` 中修改路由守卫逻辑

### 4. 样式定制
在 `src/views/Login.vue` 的 `<style>` 部分修改登录界面样式

## 测试说明

目前登录功能使用模拟数据：
- 任意符合格式要求的用户名和密码都可以登录成功
- 验证码需要正确输入（不区分大小写）
- 实际部署时需要连接真实的后端API

## 注意事项

1. **Token存储**: 目前使用localStorage存储token，生产环境建议使用更安全的方式
2. **API集成**: 需要根据实际后端API调整接口地址和参数格式
3. **错误处理**: 已添加基本错误处理，可根据需要扩展
4. **安全性**: 建议添加更多安全措施，如CSRF保护、请求加密等

## 下一步优化建议

1. 添加"记住我"功能
2. 添加找回密码功能
3. 集成真实的验证码服务
4. 添加多因素认证
5. 优化错误提示和用户体验
