/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BaseInfo: typeof import('./src/components/taskResult/BaseInfo.vue')['default']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    EventSteps: typeof import('./src/components/EventSteps.vue')['default']
    FileTable: typeof import('./src/components/taskResult/FileTable.vue')['default']
    FinishDialog: typeof import('./src/components/taskResult/FinishDialog.vue')['default']
    FirstReviewerForm: typeof import('./src/components/FirstReviewerForm.vue')['default']
    InfoDialog: typeof import('./src/components/taskResult/InfoDialog.vue')['default']
    LeftBar: typeof import('./src/components/LeftBar.vue')['default']
    NavBar: typeof import('./src/components/NavBar.vue')['default']
    PatientInfo: typeof import('./src/components/PatientInfo.vue')['default']
    PatientTable: typeof import('./src/components/taskResult/PatientTable.vue')['default']
    PrescriptionInfo: typeof import('./src/components/taskResult/PrescriptionInfo.vue')['default']
    PrescriptionTable: typeof import('./src/components/taskResult/PrescriptionTable.vue')['default']
    ReviewerFinish: typeof import('./src/components/taskResult/ReviewerFinish.vue')['default']
    ReviewerForm: typeof import('./src/components/ReviewerForm.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TopBar: typeof import('./src/components/TopBar.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
