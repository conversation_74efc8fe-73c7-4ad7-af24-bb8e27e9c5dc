import axios from "axios";
const request = axios.create({
  baseURL: import.meta.env.VITE_APP_BASEURL,
  timeout: 300000,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});
// 用于存储每个请求的CancelToken
const pendingRequests = new Map();
request.interceptors.request.use(
  (config: any) => {
    config.headers.token = "";
    if (pendingRequests.has(config.url)) {
      const cancelToken = pendingRequests.get(config.url);
      cancelToken.cancel("重复请求已取消");
      pendingRequests.delete(config.url);
    }
    const source = axios.CancelToken.source();
    config.cancelToken = source.token;
    pendingRequests.set(config.url, source);
    return config;
  },
  (error: any) => {
    Promise.reject(error);
  }
);

request.interceptors.response.use(
  async (response: any) => {
    pendingRequests.delete(response.config.url);
    return response.data;
  },
  (error: any) => {
    if (axios.isCancel(error)) {
      console.log("请求已取消");
    } else {
      console.log("其他错误：", error);
    }
    return Promise.reject(error);
  }
);
export default request;
