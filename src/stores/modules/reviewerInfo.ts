import { defineStore } from "pinia";
const reviewerInfoStore = defineStore("reviewerInfo", {
  state: () => ({
    // 初次-病种认定表信息 | 非初次认定表选择信息覆盖
    patientForm: {
      name: "", // 姓名
      gender: "", // 性别
      age: "", // 年龄
      weight: "", // 体重
      idCardNumber: "", // 身份证号
      address: "", // 患者参保地址 缺少
      phone: "", // 联系电话
      certAuthority: "", // 认定机构
      certPhysician: "", // 认定医生
      certTime: "", // 认定通过时间
      certDisease: "", // 认定通过病种
      mediGeneName: "", // 药品通用名
      mediTradName: "", // 药品商品名
      mediDose: "", // 剂量
      mediFrequency: "", // 频次
      mediRoa: "", // 给药途径
    } as { [key: string]: string },
    // 初次-处方信息
    prescriptionForm: {
      presTime: "", // 处方日期  缺少
      mediManufacturer: "", // 药品厂家 缺少
      certDisease: "", // 诊断，适用病种
      mediGeneName: "", // 药品通用名
      mediTradName: "", // 药品商品名
      mediDose: "", // 剂量
      mediFrequency: "", // 频次
      mediRoa: "", // 给药途径
    } as { [key: string]: string },
    // 非初次-处方信息
    notFirstPrescriptionForm: {
      presTime: "", // 处方日期 缺少
      mediManufacturer: "", // 药品厂家 缺少
      name: "", // 姓名
      gender: "", // 性别
      age: "", // 年龄
      idCardNumber: "", // 身份证号
      certDisease: "", // 诊断
      mediGeneName: "", // 药品通用名
      mediTradName: "", // 药品商品名
      mediDose: "", // 剂量
      mediFrequency: "", // 频次
      mediRoa: 1, // 给药途径
    } as { [key: string]: any },
    // 非初次是否选择了患者信息，做校验。
    parentInfo: {},
  }),
  actions: {},
});
export default reviewerInfoStore;
