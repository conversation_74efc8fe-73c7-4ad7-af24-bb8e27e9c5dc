import { defineStore } from "pinia";
const taskResultStore = defineStore("taskResult", {
  state: () => ({
    taskInfo: {
      taskId: "",
      status: 0,
      code: 0,
      description: null,
      patientMapUI: {
        name: "",
        idCardNumber: "",
        gender: "",
        age: 0,
      },
      diagCertFormMapUI: {
        certTime: "",
        mediGeneName: "",
        mediTradName: "",
        certDisease: "",
        mediDose: "",
        mediFrequency: "",
        mediRoa: "",
        certAuthority: "",
        certPhysician: "",
        weight: "",
      },
      prescriptionMapUI: {
        mediGeneName: "",
        mediTradName: "",
        mediDose: "",
        mediFrequency: "",
        mediRoa: "",
        certDisease: "",
      },
      formReviewInfo: null,
      prescriptionReviewInfo: null,
    },
  }),
  actions: {},
});
export default taskResultStore;
