import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/api'

// 用户信息接口
export interface UserInfo {
  id: string
  username: string
  name: string
  role: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(
    localStorage.getItem('userInfo') 
      ? JSON.parse(localStorage.getItem('userInfo')!) 
      : null
  )

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.name || userInfo.value?.username || '')
  const userRole = computed(() => userInfo.value?.role || '')

  // 登录
  const login = async (loginData: { username: string; password: string; captcha: string }) => {
    try {
      const response = await api.login(loginData)
      
      if (response.code === 200) {
        token.value = response.data.token
        userInfo.value = response.data.userInfo
        
        // 保存到localStorage
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo))
        
        return { success: true, message: '登录成功' }
      } else {
        return { success: false, message: response.message || '登录失败' }
      }
    } catch (error) {
      console.error('登录错误:', error)
      return { success: false, message: '登录失败，请检查网络连接' }
    }
  }

  // 登出
  const logout = async () => {
    try {
      await api.logout()
    } catch (error) {
      console.error('登出错误:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await api.getUserInfo()
      if (response.code === 200) {
        userInfo.value = response.data
        localStorage.setItem('userInfo', JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }

  // 检查token有效性
  const checkTokenValid = () => {
    if (!token.value) {
      return false
    }
    
    // 这里可以添加token过期检查逻辑
    // 例如解析JWT token的过期时间
    
    return true
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await api.refreshToken()
      if (response.code === 200) {
        token.value = response.data.token
        localStorage.setItem('token', response.data.token)
        return true
      }
    } catch (error) {
      console.error('刷新token失败:', error)
    }
    return false
  }

  return {
    // 状态
    token,
    userInfo,
    
    // 计算属性
    isLoggedIn,
    userName,
    userRole,
    
    // 方法
    login,
    logout,
    fetchUserInfo,
    checkTokenValid,
    refreshToken
  }
})
