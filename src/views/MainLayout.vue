<template>
  <div class="main">
    <!-- 顶部导航组件 -->
    <top-bar></top-bar>
    <div class="router-content">
      <div class="content-left">
        <LeftBar></LeftBar>
      </div>
      <div class="content-right">
        <router-view> </router-view>
      </div>
    </div>
  </div>
</template>
<script setup>
import globalStore from "@/stores/modules/global";
import websocketPrintHook from "@/hooks/websocketPrint";
const global = globalStore();
const { prtaskExecutor } = websocketPrintHook();
watchEffect(async () => {
  if (global.activeIndex >= 0) {
    await prtaskExecutor();
  }
});
</script>
<style scoped lang="scss">
.main {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  height: 100%;
  overflow: hidden;
  flex-shrink: 0;
  background: #f6f8fa;

  .router-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .content-left {
    flex-shrink: 0;
    width: 220px;
    background: #fff;
    border-right: 1px solid #f6f8fa;
  }

  .content-right {
    flex: 1;
    overflow: auto;
    display: flex;
    // align-items: center;
    justify-content: center;
  }
}

:deep(.el-dialog) {
  margin-top: 8vh;
}

:deep(.el-table__header) {
  th.el-table__cell {
    background: #f1f1f1;
    color: #333;
  }
}
</style>
