<template>
  <div class="examine-content">
    <div class="notice-message">
      <div class="notice-desc"><b>注意：</b>请在设备上放置患者处方、认定表及相关资料后，点击一键审方！</div>
    </div>
    <div class="examine-center">
      <div class="examine-title">
        <img src="@/assets/images/doctor.png" />
        <div class="title">我是国药AI审方，很高兴见到你！</div>
      </div>
      <div class="btn-exam">
        <el-button v-show="!route.query.start" size="large" type="primary" class="btn-style" @click="gotoCheck()"><img src="@/assets/images/check.png" class="btn-icon" />一键审方</el-button>
      </div>
      <event-steps v-show="route.query.start"></event-steps>
    </div>
  </div>
</template>
<script setup>
import globalStore from '@/stores/modules/global';
import websocketPrintHook from '@/hooks/websocketPrint';
import { useAuthStore } from '@/stores/modules/auth';
import router from '@/router';
import { ElMessageBox, ElMessage } from 'element-plus';

const { oneKeyCheck } = websocketPrintHook()
const route = useRoute();
const global = globalStore()
const authStore = useAuthStore()

const gotoCheck = () => {
  oneKeyCheck()
}

// 处理登出
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  }).catch(() => {
    // 用户取消
  })
}
</script>
<style lang="scss" scoped>
.examine-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .user-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .user-welcome {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    .logout-btn {
      color: #666;
      font-size: 14px;

      &:hover {
        color: #409eff;
      }
    }
  }

  .examine-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    background: #fff;
    margin: 12px;
    border-radius: 8px;
    background: #FFF;
    box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.10);
  }

  .notice-message {
    box-sizing: border-box;
    background-color: rgb(245,
        108,
        108, .1);
    border-left: 5px solid #f56c6c;
    border-radius: 4px;
    // width: 100%;
    margin: 12px 12px 0 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .notice-desc {
      font-size: 16px;
      color: #666;
      margin: 16px;
    }
  }

  .examine-title {
    display: flex;
    align-items: center;
    gap: 20px;

    img {
      width: 50px;
    }

    .title {
      font-size: 28px;
    }
  }

  .btn-exam {
    margin: 60px 0 30px 0;

    .btn-style {
      padding: 26px 40px;
      font-size: 20px;

      .btn-icon {
        width: 26px;
        margin-right: 12px;
      }
    }
  }
}
</style>