import { createApp } from "vue";
import router from "./router";
import App from "./App.vue";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import store from "./stores";
import "@/assets/scss/_variables.scss";
import "@/assets/scss/_common.scss"
import "element-plus/dist/index.css";
const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
};
app.use(store);
app.use(router);
app.mount("#app");
