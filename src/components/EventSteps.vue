<template>
  <div class="steps-list">
    <div
      :class="{
        'steps-item': true,
        'item-active': global.activeIndex >= item.activeIndex,
        'line-active': global.activeIndex > item.activeIndex,
      }"
      v-for="(item, index) in stepsData"
      :key="index"
      @click="itemClick(index)"
    >
      <div class="icon">
        <img :src="item.icon" alt="" v-if="global.activeIndex >= item.activeIndex" />
        <img :src="item.iconDis" alt="" v-if="global.activeIndex < item.activeIndex" />
      </div>
      <div class="label">{{ item.label }}</div>
      <div class="line" v-if="index < 3"></div>
    </div>
  </div>
</template>
<script setup>
import globalStore from "@/stores/modules/global";
import print from "@/assets/images/print.png";
import OCR from "@/assets/images/OCR.png";
import OCRDis from "@/assets/images/OCR_dis.png";
import patient from "@/assets/images/patient.png";
import patientDis from "@/assets/images/patient_dis.png";
import complete from "@/assets/images/complete.png";
import completeDis from "@/assets/images/complete_dis.png";
import checking from "@/assets/images/checking.png";
import checkingDis from "@/assets/images/checking_dis.png";
const global = globalStore();
const stepsData = ref([
  { label: "资料扫描", icon: print, iconDis: print, activeIndex: 1 },
  { label: "OCR识别", icon: OCR, iconDis: OCRDis, activeIndex: 3 },
  { label: "患者匹配", icon: patient, iconDis: patientDis, activeIndex: 5 },
  { label: "审方", icon: checking, iconDis: checkingDis, activeIndex: 7 },
  // { label: "审方完成", icon: complete, iconDis: completeDis, activeIndex: 8 },
]);
const itemClick = (index) => {
  // global.activeIndex = index + 1
};
</script>
<style scoped lang="scss">
.steps-list {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;
  margin: 40px 0;

  .steps-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;

    .num {
      border-radius: 50%;
      height: 30px;
      width: 30px;
      margin-bottom: 16px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid #333;
      font-weight: 600;
      background: #fff;
      position: relative;
      z-index: 2;
      cursor: pointer;
    }
    .icon {
      width: 60px;
      height: 60px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 2;
      background: #fff;
      img {
        width: 60px;
        padding: 0 4px;
      }
    }

    .label {
      font-weight: 600;
      font-display: 16px;
    }

    .line {
      left: 50%;
      right: -50%;
      position: absolute;
      height: 2px;
      top: 30px;
      background: #333;
    }
  }

  .item-active {
    .num {
      border: 2px solid #409eff;
      color: #409eff;
    }

    .label {
      color: #409eff;
    }
  }

  .line-active {
    .line {
      background: #409eff;
    }
  }
}
</style>
