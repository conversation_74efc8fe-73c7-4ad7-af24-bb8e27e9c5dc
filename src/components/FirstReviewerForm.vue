<template>
  <el-dialog
    v-model="dialogVisible"
    width="80%"
    title="患者匹配"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <!-- <el-button type="primary" @click="viewInfoFun" class="view-info"
      >查看资料</el-button
    > -->
    <div class="right-content" v-loading="loading">
      <div class="right-center-content">
        <el-form
          :model="formData"
          :rules="rules"
          ref="formRef"
          label-width="110"
          label-position="left"
          class="right-center-form"
        >
          <div class="p-header">
            <el-icon>
              <Menu />
            </el-icon>
            <span>病种认定表信息</span>
          </div>
          <div class="card">
            <div
              v-for="(item, index) in columnsForPatient"
              :key="index"
              class="input-item"
            >
              <!-- :class="{ 'is-required': item.prop == 'age' }" -->
              <el-form-item
                :prop="'patientForm.' + item.prop"
                :label="item.label"
              >
                <!-- 输入框 -->
                <el-input
                  v-model.trim="formData.patientForm[item.prop]"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  :maxlength="item.maxlength"
                  v-if="item.type === 'input' && !item.unit"
                  @blur="inputBlur(item.prop)"
                />
                <!-- 输入框 带单位 -->
                <el-input
                  v-model.trim="formData.patientForm[item.prop]"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  :maxlength="item.maxlength"
                  v-if="item.type === 'input' && item.unit"
                >
                  <template #append>{{ item.unit }}</template>
                </el-input>
                <!-- 下拉框 -->
                <el-select
                  v-model.trim="formData.patientForm[item.prop]"
                  :placeholder="item.placeholder"
                  :filterable="item.filterable"
                  :disabled="item.disabled"
                  v-if="item.type === 'select'"
                >
                  <el-option
                    v-for="(item, index) in item.options"
                    :key="index"
                    :label="item.label"
                    :value="item.label"
                  />
                </el-select>
                <!-- 单选框 -->
                <el-radio-group
                  v-model.trim="formData.patientForm[item.prop]"
                  v-if="item.type === 'radio'"
                >
                  <el-radio
                    v-for="(item, index) in item.options"
                    :key="index"
                    :value="item.label"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
                <!-- 日期 -->
                <el-date-picker
                  v-model.trim="formData.patientForm[item.prop]"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  v-if="item.type === 'date'"
                  type="date"
                  style="width: 100%"
                />
                <div class="item-line" v-if="item.type === 'inputAndselect'">
                  <!-- 输入框 -->
                  <el-input
                    v-model="mediGeneNameForPatient"
                    :placeholder="item.placeholder"
                    :disabled="item.disabled"
                    style="flex: 1"
                  />
                  <!-- 下拉框 -->
                  <el-select
                    v-model.trim="formData.patientForm[item.prop]"
                    :placeholder="item.placeholder"
                    :filterable="item.filterable"
                    style="flex: 1"
                  >
                    <el-option
                      v-for="(item, index) in item.options"
                      :key="index"
                      :label="item.label"
                      :value="item.label"
                    />
                  </el-select>
                </div>
              </el-form-item>
            </div>
            <div class="warning-text">
              请核对以上病种认定表信息，若有误请协助修正。
            </div>
          </div>
          <div class="p-header">
            <el-icon>
              <Menu />
            </el-icon>
            <span>处方信息</span>
          </div>
          <div class="card">
            <div
              v-for="(item, index) in columnsForPrescription"
              :key="index"
              class="input-item"
            >
              <el-form-item
                :prop="'prescriptionForm.' + item.prop"
                :label="item.label"
              >
                <!-- 输入框 -->
                <el-input
                  v-model.trim="formData.prescriptionForm[item.prop]"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  :maxlength="item.maxlength"
                  v-if="item.type === 'input'"
                />
                <!-- 下拉框 -->
                <el-select
                  v-model.trim="formData.prescriptionForm[item.prop]"
                  :placeholder="item.placeholder"
                  :filterable="item.filterable"
                  :disabled="item.disabled"
                  v-if="item.type === 'select'"
                >
                  <el-option
                    v-for="(item, index) in item.options"
                    :key="index"
                    :label="item.label"
                    :value="item.label"
                  />
                </el-select>
                <!-- 日期 -->
                <el-date-picker
                  v-model.trim="formData.prescriptionForm[item.prop]"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  v-if="item.type === 'date'"
                  type="date"
                  style="width: 100%"
                />
                <div class="item-line" v-if="item.type === 'inputAndselect'">
                  <!-- 输入框 -->
                  <el-input
                    v-model="mediGeneNameForPrescription"
                    :placeholder="item.placeholder"
                    :disabled="item.disabled"
                    style="flex: 1"
                  />
                  <!-- 下拉框 -->
                  <el-select
                    v-model.trim="formData.prescriptionForm[item.prop]"
                    :placeholder="item.placeholder"
                    :filterable="item.filterable"
                    style="flex: 1"
                  >
                    <el-option
                      v-for="(item, index) in item.options"
                      :key="index"
                      :label="item.label"
                      :value="item.label"
                    />
                  </el-select>
                </div>
              </el-form-item>
            </div>
            <div class="warning-text">
              请核对以上处方信息，若有误请协助修正。
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="btns">
        <el-button type="default" @click="cancelFun">取消</el-button>
        <el-button type="primary" @click="submitFun">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <info-dialog ref="infoDialogRef" v-model="infoVisible"></info-dialog>
</template>
<script setup>
import rulesHook from "@/hooks/rules";
import commonMethodsHook from "@/hooks/commonMethods";
import reviewerCheckHook from "@/hooks/reviewerCheck";
import reviewerInfoStore from "@/stores/modules/reviewerInfo";
import globalStore from "@/stores/modules/global";
const { filterMediGeneName } = reviewerCheckHook();
import api from "@/api";
const infoVisible = ref(false);
const infoDialogRef = ref();
const viewInfoFun = () => {
  infoVisible.value = true;
  infoDialogRef.value.getInfoList();
};
const global = globalStore();
const reviewerInfo = reviewerInfoStore();
const formData = reactive({
  patientForm: {},
  prescriptionForm: {},
});
const dialogVisible = true;
const { rules, columnsForPatient, columnsForPrescription } = rulesHook();
const { cancelFunc, submitFunc } = reviewerCheckHook();
const { getAgeByIdCard } = commonMethodsHook();
const formRef = ref();
const inputBlur = (prop) => {
  // if (prop === "idCardNumber") {
  //   formData.patientForm.age = getAgeByIdCard(
  //     formData.patientForm.idCardNumber
  //   );
  // }
};
const cancelFun = () => {
  cancelFunc(formRef.value, 1);
};
const submitFun = () => {
  submitFunc(formRef.value, formData, 1);
};
let mediGeneNameForPatient = ref("");
let mediGeneNameForPrescription = ref("");
// 模糊过滤药品通用名
const loading = ref(false);
watchEffect(() => {
  formData.patientForm = reviewerInfo.patientForm;
  formData.prescriptionForm = reviewerInfo.prescriptionForm;
});
watchEffect(async () => {
  loading.value = true;
  // 药品通用名显示
  // setTimeout(async () => {
  mediGeneNameForPatient.value = reviewerInfo.patientForm?.mediGeneName;
  mediGeneNameForPrescription.value =
    reviewerInfo.prescriptionForm?.mediGeneName;
  formData.patientForm.mediGeneName = await filterMediGeneName(
    mediGeneNameForPatient.value
  );
  formData.prescriptionForm.mediGeneName = await filterMediGeneName(
    mediGeneNameForPrescription.value
  );
  loading.value = false;
  // }, 1000);
});
</script>
<style scoped lang="scss">
.right-content {
  height: 63vh;
  overflow: auto;
  .card {
    display: flex;
    flex-wrap: wrap;
    gap: 0 10%;
    background: #f6f8fa;

    .input-item {
      width: 40%;
    }

    .warning-text {
      color: red;
      width: 100%;
    }
  }
}
.item-line {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}
.no-data {
  margin: 12px;
}
.view-info {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 9999;
}
</style>
