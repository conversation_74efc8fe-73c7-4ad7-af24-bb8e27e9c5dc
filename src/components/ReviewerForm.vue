<template>
  <el-dialog
    v-model="dialogVisible"
    width="80%"
    title="患者匹配"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <!-- <el-button type="primary" @click="viewInfoFun" class="view-info"
      >查看资料</el-button
    > -->
    <div class="right-content" v-loading="loading">
      <div class="right-center-content">
        <el-form
          :model="formData"
          :rules="rules"
          ref="formRef"
          label-width="110"
          label-position="left"
        >
          <div class="p-header">
            <el-icon>
              <Menu />
            </el-icon>
            <span>患者信息</span>
          </div>
          <div class="card">
            <patient-info ref="patientInfoRef"></patient-info>
          </div>
          <div class="p-header">
            <el-icon>
              <Menu />
            </el-icon>
            <span>处方信息</span>
          </div>
          <div class="card">
            <div
              v-for="(item, index) in noFirstColumnsForPrescription"
              :key="index"
              class="input-item"
            >
              <el-form-item
                :prop="'notFirstPrescriptionForm.' + item.prop"
                :label="item.label"
              >
                <div v-if="item.type == 'inputAdnBtn'" class="moreElement">
                  <el-input
                    v-model.trim="formData.notFirstPrescriptionForm[item.prop]"
                    :placeholder="item.placeholder"
                    :maxlength="item.maxlength"
                    style="flex: 1"
                    :disabled="item.disabled"
                  />
                  <el-button
                    type="primary"
                    size="small"
                    @click="editName(item)"
                    >{{ item.btnText }}</el-button
                  >
                </div>
                <!-- 输入框 -->
                <el-input
                  v-model.trim="formData.notFirstPrescriptionForm[item.prop]"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  :maxlength="item.maxlength"
                  v-if="item.type === 'input'"
                  @blur="inputBlur(item.prop)"
                />
                <!-- 下拉框 -->
                <el-select
                  v-model.trim="formData.notFirstPrescriptionForm[item.prop]"
                  :placeholder="item.placeholder"
                  :filterable="item.filterable"
                  :disabled="item.disabled"
                  v-if="item.type === 'select'"
                  :filter-method="
                    filterMethodFun(
                      item,
                      formData.notFirstPrescriptionForm[item.prop]
                    )
                  "
                >
                  <el-option
                    v-for="(item, index) in item.options"
                    :key="index"
                    :label="item.label"
                    :value="item.label"
                  />
                </el-select>
                <!-- 单选框 -->
                <el-radio-group
                  v-model.trim="formData.notFirstPrescriptionForm[item.prop]"
                  v-if="item.type === 'radio'"
                >
                  <el-radio
                    v-for="(item, index) in item.options"
                    :key="index"
                    :value="item.label"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
                <!-- 日期 -->
                <el-date-picker
                  v-model.trim="formData.notFirstPrescriptionForm[item.prop]"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :placeholder="item.placeholder"
                  :disabled="item.disabled"
                  v-if="item.type === 'date'"
                  type="date"
                  style="width: 100%"
                />
                <div class="item-line" v-if="item.type === 'inputAndselect'">
                  <!-- 输入框 -->
                  <el-input
                    v-model="mediGeneNameForPrescription"
                    :placeholder="item.placeholder"
                    :disabled="item.disabled"
                    style="flex: 1"
                  />
                  <!-- 下拉框 -->
                  <el-select
                    v-model.trim="formData.notFirstPrescriptionForm[item.prop]"
                    :placeholder="item.placeholder"
                    :filterable="item.filterable"
                    style="flex: 1"
                  >
                    <el-option
                      v-for="(item, index) in item.options"
                      :key="index"
                      :label="item.label"
                      :value="item.label"
                    />
                  </el-select>
                </div>
              </el-form-item>
            </div>
            <div class="warning-text">
              请核对以上处方信息，若有误请协助修正。
            </div>
          </div>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="btns">
        <el-button type="default" @click="cancelFun">取消</el-button>
        <el-button type="primary" @click="submitFun">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <info-dialog ref="infoDialogRef" v-model="infoVisible"></info-dialog>
</template>
<script setup>
import rulesHook from "@/hooks/rules";
import commonMethodsHook from "@/hooks/commonMethods";
import reviewerInfoStore from "@/stores/modules/reviewerInfo";
import reviewerCheckHook from "@/hooks/reviewerCheck";
import globalStore from "@/stores/modules/global";
const infoVisible = ref(false);
const infoDialogRef = ref();
const viewInfoFun = () => {
  infoVisible.value = true;
  infoDialogRef.value.getInfoList();
};
const { getAgeByIdCard } = commonMethodsHook();
const global = globalStore();
const reviewerInfo = reviewerInfoStore();
const formData = reactive({
  patientForm: reviewerInfo.patientForm,
  notFirstPrescriptionForm: reviewerInfo.notFirstPrescriptionForm,
});
const dialogVisible = true;
const { rules, noFirstColumnsForPrescription } = rulesHook();
const { cancelFunc, submitFunc, filterMediGeneName } = reviewerCheckHook();
const formRef = ref();
const inputBlur = (prop) => {
  // 通过身份证号码计算年龄
  // if (prop === "idCardNumber") {
  //   formData.notFirstPrescriptionForm.age = getAgeByIdCard(
  //     formData.notFirstPrescriptionForm.idCardNumber
  //   );
  // }
};
const patientInfoRef = ref();
// 修改姓名
const editName = async (item) => {
  if (item.disabled) {
    item.btnText = "保存";
    item.disabled = false;
  } else {
    formRef.value.validateField("notFirstPrescriptionForm.name", (valid) => {
      if (valid) {
        item.disabled = true;
        item.btnText = "修改";
        // 通过修改的姓名调用患者信息列表
        patientInfoRef.value.getTableData(
          formData.notFirstPrescriptionForm.name
        );
      }
    });
  }
};
const filterMethodFun = (item, value) => {};
const cancelFun = () => {
  cancelFunc(formRef.value, 2);
};
const submitFun = () => {
  submitFunc(formRef.value, formData, 2);
};
let mediGeneNameForPrescription = ref("");
const loading = ref(false);
watchEffect(async () => {
  loading.value = true;
  // setTimeout(async () => {
  mediGeneNameForPrescription.value =
    reviewerInfo.notFirstPrescriptionForm.mediGeneName;
  formData.notFirstPrescriptionForm.mediGeneName = await filterMediGeneName(
    mediGeneNameForPrescription.value
  );
  loading.value = false;
  // }, 1000);
});
</script>
<style scoped lang="scss">
.right-content {
  height: 63vh;
  overflow: auto;

  .card {
    display: flex;
    flex-wrap: wrap;
    gap: 0 10%;
    background: #f6f8fa;

    .input-item {
      width: 40%;
    }

    .warning-text {
      color: red;
      width: 100%;
    }
  }
}
.moreElement {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0 10px;
  width: 100%;
}
.item-line {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}
.view-info {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 9999;
}
</style>
