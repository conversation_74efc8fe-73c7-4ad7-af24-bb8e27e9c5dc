<template>
  <div class="left-content">
    <div class="left-btn">
      <!-- <el-tooltip content="请在设备上放置患者处方、认定表及相关资料后，点击一键审方！"> -->
      <el-button
        type="primary"
        size="large"
        style="width: 100%"
        @click="gotoHome()"
      >
        <img src="@/assets/images/check.png" class="btn-icon" />
        一键审方
      </el-button>
      <!-- </el-tooltip> -->
    </div>
    <div class="left-list">
      <div
        :class="{
          'list-item': true,
          'active-bg': global.currentId === item.id,
        }"
        v-for="(item, index) in global.leftList"
        :key="index"
        @click="gotoPage(item, index)"
      >
        <span>{{
          item.patientMapUI?.name ? item.patientMapUI?.name + "-" : ""
        }}</span
        ><span>{{ statusChange(item.status) }}</span
        ><span>{{
          item.formReviewScore !== null
            ? item.formReviewScore === 0
              ? "-无效"
              : "-有效"
            : ""
        }}</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import globalStore from "@/stores/modules/global";
import websocketPrintHook from "@/hooks/websocketPrint";
import commonMethods from "@/hooks/commonMethods";
const global = globalStore();
const router = useRouter();
const route = useRoute();
const { statusChange } = commonMethods();
const { oneKeyCheck } = websocketPrintHook();
const gotoHome = async () => {
  router.push({ name: "home", query: { start: true } }).then(() => {
    oneKeyCheck();
  });
};
const gotoPage = (item) => {
  global.currentId = item.id;
  if (item.status == 8) {
    router
      .push({
        name: "detail",
        params: { id: item.id },
      })
      .then(() => {
        global.currentId = item.id;
      })
      .catch((e) => {
        console.log(e);
      });
  } else {
    router
      .replace({
        name: "progress",
        params: { id: item.id },
      })
      .then(() => {
        global.currentId = item.id;
      })
      .catch((e) => {
        console.log(e);
      });
  }
};
watch(
  () => route.params,
  (newVal) => {
    if (newVal) {
      global.currentId = newVal.id;
    } else {
      global.currentId = "";
    }
  }
);
</script>
<style scoped lang="scss">
.left-content {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .left-btn {
    flex-shrink: 0;
    padding: 12px;
    margin-bottom: 12px;
    border-bottom: 1px solid #eee;
  }

  .left-list {
    flex: 1;
    overflow: auto;
    text-align: right;

    .list-item {
      padding: 12px 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    .list-item:hover {
      color: #409eff;
      background: #f6f8fa;
    }

    .active-bg {
      color: #409eff;
      background: #f6f8fa;
      font-weight: 500;
    }
  }

  .btn-icon {
    width: 20px;
    margin-right: 12px;
  }
}
</style>
