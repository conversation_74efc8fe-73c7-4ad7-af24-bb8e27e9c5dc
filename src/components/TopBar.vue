<template>
  <div class="topbar-menu">
    <div class="menu-left">
      <div class="logo">
        <img src="@/assets/images/logo.png" />
        <div class="menu-title" @click="initPage()">AI审方</div>
      </div>
      <!-- <div class="meun-history">审方记录</div> -->
    </div>
    <div class="menu-right">
      <!-- <div class="notice">
        <el-icon>
          <img src="@/assets/images/notice.png" />
        </el-icon>
        <span>消息</span>
      </div> -->
      <div class="pharmacy">
        <img src="@/assets/images/pharmacy.png" />
        <span>SPS</span><span class="topPlus">+</span><span>药房</span>
      </div>
      <div class="user">
        <img src="@/assets/images/user.png" />
        <span>李亚静</span>
      </div>
      <div class="login-out">
        <img src="@/assets/images/close.png" />
        <span>注销</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import globalStore from '@/stores/modules/global'
const global = globalStore()
const router = useRouter()
const initPage = () => {
  router.push({ name: 'home' })
}
</script>

<style scoped lang="scss">
.topbar-menu {
  background: #333;
  color: #fff;
  padding: 12px 32px;
  display: flex;
  align-items: center;
  .menu-left, .menu-right {
    display: flex;
    align-items: center;
    gap: 24px;
  }
  .menu-left {
    flex: 1;
    .logo {
      img {
        display: block;
        width: 40px;
      }
      display: flex;
      align-items: center;
      font-size: 20px;
      gap: 10px;
      cursor: pointer;
    }
  }
  .notice, .pharmacy, .user, .login-out {
    display: flex;
    align-items: flex-end;
    gap: 4px;
    cursor: pointer;
    img {
      height: 16px;
      width: 16px;
      display: block;
    }
  }
  .meun-history {
    cursor: pointer;
  }
}
.topPlus {
  position: relative;
  top: -6px;
  left: -3px;
  font-size: 10px;
}
</style>
