<template>
  <el-dialog
    title="查看资料"
    v-model="dialogVisible"
    width="50%"
    :close-on-click-modal="false"
  >
    <el-table border :data="tableData" width="100%" v-loading="loading">
      <el-table-column prop="id" label="id" width="120"></el-table-column>
      <el-table-column label="类型" width="200">
        <template #default="scope">
          <!-- 1:表示认定表;2:表示处方;3:表示检查报告;-1:表示未知类型 -->
          <span v-if="scope.row.type === 1">认定表</span>
          <span v-else-if="scope.row.type === 2">处方</span>
          <span v-else-if="scope.row.type === 3">检查报告</span>
          <span v-else-if="scope.row.type === -1">未知类型</span>
        </template>
      </el-table-column>
      <el-table-column prop="caption" label="标题"></el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button type="primary" @click="editInfoFun(scope.row)" size="small"
            >修改</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
  <el-dialog
    title="修改信息"
    v-model="visible"
    width="50%"
    :close-on-click-modal="false"
  >
    <el-form :model="formData">
      <el-form-item label="" prop="contentFormatted">
        <el-input
          type="textarea"
          v-model="formData.contentFormatted"
          :rows="6"
        ></el-input>
        <!-- <div>
          <json-editor-vue class="editor" v-model="jsonData" />
        </div> -->
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="updateReviewInfo">确 定</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import api from "@/api";
import globalStore from "@/stores/modules/global";
// import VueJsonPretty from "vue-json-pretty";
// import "vue-json-pretty/lib/styles.css";
// import JsonEditorVue from "json-editor-vue3";
const jsonData = ref();
const loading = ref(false);
const global = globalStore();
const dialogVisible = defineModel();
const tableData = ref([]);
const visible = ref(false);
const formData = ref({
  id: "",
  contentFormatted: "",
});
const getInfoList = async () => {
  loading.value = true;
  let result = await api.taskMaterialApi({ taskId: global.currentId });
  if (result.errcode == 0) {
    tableData.value = result.detail;
  } else {
    ElMessage.error(result.chMsg);
  }
  loading.value = false;
};
const editInfoFun = (row) => {
  visible.value = true;
  formData.value.id = row.id;
  formData.value.contentFormatted = row.contentFormatted;
  // jsonData.value = JSON.parse(row.contentFormatted);
};
const updateReviewInfo = async () => {
  const result = await api.updateTaskMaterialApi({
    id: formData.value.id,
    contentFormatted: formData.value.contentFormatted,
  });
  if (result.errcode === 0) {
    visible.value = false;
    await getInfoList();
    ElMessage.success("信息修改成功");
  } else {
    ElMessage.error(result.chMsg);
  }
};
defineExpose({
  getInfoList,
});
</script>
<style scoped lang="scss">
.editor {
  height: 400px;
}
</style>
