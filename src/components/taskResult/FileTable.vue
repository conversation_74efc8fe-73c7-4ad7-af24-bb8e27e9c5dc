<template>
  <div class="p-header">
    <el-icon>
      <Menu />
    </el-icon>
    <span>资料详情</span>
  </div>
  <div class="card">
    <el-table border :data="tableData" width="100%" v-loading="loading">
      <el-table-column prop="id" label="编号" width="100"></el-table-column>
      <el-table-column label="文件类型" width="120">
        <template #default="scope">
          <!-- 1:表示认定表;2:表示处方;3:表示检查报告;-1:表示未知类型 -->
          <span v-if="scope.row.type === 1">认定表</span>
          <span v-else-if="scope.row.type === 2">处方</span>
          <span v-else-if="scope.row.type === 3">检查报告</span>
          <span v-else-if="scope.row.type === -1">未知类型</span>
        </template>
      </el-table-column>
      <el-table-column label="是否已上传" width="120">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.imageFname">已上传</el-tag>
          <el-tag type="danger" v-else>未上传</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="docTime"
        label="上传时间"
        width="200"
      ></el-table-column>
      <el-table-column prop="caption" label="文件名称"></el-table-column>
    </el-table>
  </div>
</template>
<script setup>
import api from "@/api";
import globalStore from "@/stores/modules/global";
const global = globalStore();
const tableData = ref([]);
const loading = ref(false);
const getInfoList = async () => {
  loading.value = true;
  try {
    let result = await api.taskMaterialApi({ taskId: global.currentId });
    if (result.errcode == 0) {
      tableData.value = result.detail;
    } else {
      ElMessage.error(result.chMsg);
    }
  } catch (error) {
    ElMessage.error(error.chMsg);
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  getInfoList();
});
</script>
