<template>
  <el-table
    border
    class="card-table"
    :data="taskResult.taskInfo.prescriptionReviewInfo"
    empty-text="暂无数据"
  >
    <el-table-column prop="ruleName" label="项目" width="200"></el-table-column>
    <el-table-column
      prop="reviewContentInPrescription"
      label="处方信息"
      width="240"
    ></el-table-column>
    <el-table-column
      prop="reviewContentInForm"
      label="认定信息"
      width="240"
      :show-overflow-tooltip="{
        effect: 'dark',
        width: '200px' /* 部分版本可能无效 */,
      }"
    ></el-table-column>
    <el-table-column label="是否一致" width="120">
      <template #default="scope">
        <el-tag
          v-if="
            scope.row.isContentMatch === true &&
            scope.row.reviewContentInPrescription &&
            scope.row.reviewContentInForm
          "
          type="success"
          >一致</el-tag
        >
        <el-tag v-if="scope.row.isContentMatch === false" type="danger"
          >不一致</el-tag
        >
      </template>
    </el-table-column>
    <el-table-column
      prop="ruleContent"
      label="规则库"
      :show-overflow-tooltip="{
        effect: 'dark',
        width: '200px' /* 部分版本可能无效 */,
      }"
    ></el-table-column>
    <el-table-column prop="ruleScore" label="是否匹配" width="120">
      <template #default="scope">
        <div v-if="scope.row.ruleScore != -1">
          <el-tag
            v-if="
              scope.row.ruleName == '限定支付范围' ||
              scope.row.ruleName == '支付周期限制' ||
              scope.row.ruleName == '支付次数限制'
            "
            type="warning"
            >提示</el-tag
          >
          <div v-else>
            <el-tag v-if="scope.row.ruleScore == 100" type="success"
              >匹配<el-tooltip
                effect="dark"
                :content="scope.row.ruleScoreReason"
                placement="top-start"
                v-if="scope.row.ruleScoreReason"
                ><el-icon><InfoFilled /></el-icon> </el-tooltip
            ></el-tag>
            <el-tag v-else type="danger"
              >不匹配
              <el-tooltip
                effect="dark"
                :content="scope.row.ruleScoreReason"
                placement="top-start"
                ><el-icon><InfoFilled /></el-icon> </el-tooltip
            ></el-tag>
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup>
import taskResultStore from "@/stores/modules/taskResult";
const taskResult = computed(() => taskResultStore());
</script>
<style lang="scss" scoped>
:deep(.el-tag__content) {
  display: flex;
  gap: 2px;
  align-items: center;
}
</style>
