<template>
  <div class="right-content" v-loading="loading">
    <div class="right-top-nav-bar">
      <nav-bar>
        <div class="nav-bar-item">
          <span class="nav-title">审方结果</span>
          <el-button type="primary" @click="editStatusFun(4)"
            >补充资料</el-button
          >
          <!-- <el-button type="primary" @click="editStatusFun(6, true)">重新审方</el-button> -->
          <!-- <el-button type="success" @click="viewInfoFun()">查看资料</el-button> -->
          <div class="gap"></div>
        </div>
      </nav-bar>
    </div>
    <div class="right-center-content">
      <!-- 基本信息 -->
      <base-info></base-info>
      <!-- 处方信息 -->
      <prescription-info></prescription-info>
      <!-- 认定表详情 -->
      <div class="p-header" v-if="taskResult.taskInfo.diagCertFormMapUI">
        <el-icon>
          <Menu />
        </el-icon>
        <span>审核详情 - 认定表审核详情</span>
      </div>
      <div class="card" v-if="taskResult.taskInfo.diagCertFormMapUI">
        <div class="card-line">
          <div>
            <el-button
              type="primary"
              @click="editStateBtn(100, 1)"
              :disabled="taskResult.taskInfo.formReviewScore !== null"
              >有效</el-button
            >
          </div>
          <div>
            <el-button
              type="default"
              @click="editStateBtn(0, 1)"
              :disabled="taskResult.taskInfo.formReviewScore !== null"
              >无效</el-button
            >
          </div>
          <div class="error">
            请协助判断认定表有效性，保障信息准确性，简化后续审方！
          </div>
        </div>
        <div class="card-list">
          <div class="list-item color-red">
            <div class="item-label">
              {{
                taskResult.taskInfo.formReviewScore !== null
                  ? taskResult.taskInfo.formReviewScore === 0
                    ? "无效"
                    : "有效"
                  : "未确认是否有效"
              }}
            </div>
            <div class="item-value"></div>
          </div>
          <div class="list-item color-green">
            <div class="item-label">一致项：</div>
            <div class="item-value">5项</div>
          </div>
          <div class="list-item color-red">
            <div class="item-label">不一致项：</div>
            <div class="item-value">5项</div>
          </div>
          <div class="list-item color-red">
            <div class="item-label">提示信息：</div>
            <div class="item-value color-red">5项</div>
          </div>
          <div class="list-item">
            <div class="item-label">审核时间：</div>
            <div class="item-value">{{ "2025-05-20" }}</div>
          </div>
        </div>
        <patient-table></patient-table>
      </div>
      <!-- 处方详情 -->
      <div class="p-header" v-if="taskResult.taskInfo.prescriptionReviewInfo">
        <el-icon>
          <Menu />
        </el-icon>
        <span>审核详情 - 处方与认定表一致性检查</span>
      </div>
      <div class="card" v-if="taskResult.taskInfo.prescriptionReviewInfo">
        <div class="card-line">
          <div>
            <el-button
              type="primary"
              @click="editStateBtn(100, 2)"
              :disabled="taskResult.taskInfo.presReviewScore !== null"
              >有效</el-button
            >
          </div>
          <div>
            <el-button
              type="default"
              @click="editStateBtn(0, 2)"
              :disabled="taskResult.taskInfo.presReviewScore !== null"
              >无效</el-button
            >
          </div>
          <div class="error">
            请协助判断处方有效性，保障信息准确性，简化后续审方！
          </div>
        </div>
        <div class="card-list">
          <div class="list-item color-red">
            <div class="item-label">
              {{
                taskResult.taskInfo.presReviewScore !== null
                  ? taskResult.taskInfo.presReviewScore === 0
                    ? "无效"
                    : "有效"
                  : "未确认是否有效"
              }}
            </div>
            <div class="item-value"></div>
          </div>
          <div class="list-item color-green">
            <div class="item-label">一致项：</div>
            <div class="item-value">5项</div>
          </div>
          <div class="list-item color-red">
            <div class="item-label">不一致项：</div>
            <div class="item-value">5项</div>
          </div>
          <div class="list-item color-red">
            <div class="item-label">提示信息：</div>
            <div class="item-value color-red">5项</div>
          </div>
          <div class="list-item">
            <div class="item-label">审核时间：</div>
            <div class="item-value">{{ "2025-05-20" }}</div>
          </div>
        </div>
        <prescription-table></prescription-table>
      </div>
      <file-table></file-table>
    </div>
  </div>
  <finish-dialog v-model="dialogVisible" ref="finishRef"></finish-dialog>
  <info-dialog ref="infoDialogRef" v-model="infoVisible"></info-dialog>
</template>
<script setup>
import { onBeforeRouteLeave } from "vue-router";
import globalStore from "@/stores/modules/global";
import api from "@/api";
import taskResultStore from "@/stores/modules/taskResult";
import websocketPrintHook from "@/hooks/websocketPrint";
const { editStatusFun, getTaskResultFun, prtaskExecutor } = websocketPrintHook();
const taskResult = taskResultStore();
const infoVisible = ref(false);
const loading = ref(false);
const route = useRoute();
const global = globalStore();
const finishRef = ref();
const dialogVisible = ref(false);
const infoDialogRef = ref();
const openDialog = (next) => {
  // 初次审方如果没有页面确认信息，离开界面需要弹窗
  if (
    taskResult.taskInfo.formReviewScore === null ||
    taskResult.taskInfo.presReviewScore === null
  ) {
    dialogVisible.value = true;
    throw new Error("没有确认信息");
  } else {
    next();
  }
};
const viewInfoFun = () => {
  infoVisible.value = true;
  infoDialogRef.value.getInfoList();
};
// 有效、无效处理
const editStateBtn = async (state, type) => {
  let params = {
    taskId: global.currentId,
    formReviewScore: taskResult.taskInfo?.formReviewScore,
    presReviewScore: taskResult.taskInfo?.presReviewScore,
  };
  // 没有认定表信息，传-1
  if (!taskResult.taskInfo.diagCertFormMapUI) {
    params.formReviewScore = -1;
  }
  if (type == 1) {
    params.formReviewScore = state;
    if (state === 0) {
      // 认定表无效，处方只能无效
      params.presReviewScore = 0;
    }
  } else if (type == 2) {
    params.presReviewScore = state;
  }
  let result = await api.updateReviewInfoApi(params);
  if (result.errcode === 0) {
    taskResult.taskInfo = await getTaskResultFun();
    prtaskExecutor();
    ElMessage.success("有效性确认成功");
  } else {
    ElMessage.error(result.chMsg);
  }
};
onMounted(async () => {
  loading.value = true;
  try {
    const result = await api.getTaskResultApi({
      taskId: global.currentId,
    });
    if (result.errcode == 0) {
      taskResult.taskInfo = result.detail;
    } else {
      ElMessage.error(result.chMsg);
    }
  } catch (error) {
  } finally {
    loading.value = false;
  }
});
onBeforeRouteLeave((to, from, next) => {
  openDialog(next);
});
onBeforeRouteUpdate((to, from, next) => {
  openDialog(next);
});
</script>
<style scoped lang="scss">
:deep(.right-content) {
  .el-popper {
    max-width: 40% !important;
  }
}
.right-top-nav-bar {
  .nav-bar-item {
    width: 100%;
    display: flex;
    align-items: center;
  }
  .nav-title {
    flex: 1;
    font-size: 16px;
  }
  .gap {
    width: 12px;
  }
}
.color-red {
  color: red;
}
.color-green {
  color: green;
}
</style>
