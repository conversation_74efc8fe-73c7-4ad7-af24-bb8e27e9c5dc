<template>
  <div class="card">
    <div class="card-list">
      <div v-for="(item, index) in prescriptionInfo" :key="index" class="list-item">
        <div class="item-label">{{ item.name }}</div>
        <div class="item-value">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import taskResultStore from '@/stores/modules/taskResult';
const prescriptionInfo = ref([]);
const taskResult = taskResultStore();
watchEffect(() => {
  prescriptionInfo.value = [
    { name: "处方日期：", value:  taskResult.taskInfo.prescriptionMapUI?.presTime },
    { name: "药品通用名：", value: taskResult.taskInfo.prescriptionMapUI?.mediGeneName },
    { name: "药品商品名：", value: taskResult.taskInfo.prescriptionMapUI?.mediTradName },
    { name: "药品厂家：", value: taskResult.taskInfo.prescriptionMapUI?.mediManufacturer},
  ];
});
</script>
