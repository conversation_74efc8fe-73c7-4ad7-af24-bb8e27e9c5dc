<template>
  <el-dialog
    title=""
    v-model="visible"
    width="30%"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div class="title">
      请确认认定表、处方有效性，保障信息准确性，简化后续审方。
    </div>
    <el-form
      :model="formData"
      ref="formRef"
      :rules="rules"
      label-position="right"
      label-width="80"
    >
      <el-form-item
        label="认定表："
        prop="isConfirm"
        v-if="taskResult.taskInfo.formReviewScore === null"
      >
        <el-radio-group v-model="formData.isConfirm" @change="changeRadio">
          ">
          <el-radio
            v-for="item in radioList"
            :key="item.value"
            :value="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="处方："
        prop="prescription"
        v-if="taskResult.taskInfo.presReviewScore === null"
      >
        <el-radio-group v-model="formData.prescription">
          <el-radio
            v-for="item in radioList"
            :key="item.value"
            :value="item.value"
            >{{ item.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="default" @click="cancelFun">结束本次审方</el-button>
      <el-button type="primary" @click="submitFun">确定</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import globalStore from "@/stores/modules/global";
import taskResultStore from "@/stores/modules/taskResult";
import websocketPrintHook from "@/hooks/websocketPrint";
import api from "@/api";
const router = useRouter();
const { finishTaskFun, prtaskExecutor, getTaskResultFun } =
  websocketPrintHook();
const global = globalStore();
const taskResult = taskResultStore();
const rules = {
  isConfirm: [{ required: true, message: "不能为空", trigger: "change" }],
  prescription: [{ required: true, message: "不能为空", trigger: "change" }],
};
const visible = defineModel();
const formData = reactive({
  isConfirm: 0,
  prescription: 0,
});
const radioList = [
  { label: "有效", value: 100 },
  { label: "无效", value: 0 },
];
const changeRadio = () => {
  if (formData.isConfirm === 0) {
    formData.prescription = 0;
  }
};
const formRef = ref();
const cancelFun = () => {
  visible.value = false;
  finishTaskFun();
  router.replace({ name: "home" });
};
const submitFun = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      let params = {
        taskId: global.currentId,
        formReviewScore: formData.isConfirm,
        presReviewScore: formData.prescription,
      };
      try {
        const result = await api.updateReviewInfoApi(params);
        if (result.errcode === 0) {
          ElMessage.success("修改成功");
          taskResult.taskInfo = await getTaskResultFun();
          prtaskExecutor();
        } else {
          ElMessage.error(result.chMsg);
        }
      } catch (error) {
        console.log(error);
      } finally {
        visible.value = false;
      }
    }
  });
};
watch(
  () => visible,
  (val) => {
    if (val) {
      formData.isConfirm = taskResult.taskInfo.formReviewScore;
      formData.prescription = taskResult.taskInfo.presReviewScore;
    }
  }
);
</script>
<style scoped lang="scss">
.title {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
  text-align: center;
}
</style>
