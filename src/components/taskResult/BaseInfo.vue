<template>
  <div class="p-header">
    <el-icon>
      <Menu />
    </el-icon>
    <span>基本信息</span>
  </div>
  <div class="card">
    <div class="card-list">
      <div v-for="(item, index) in info" :key="index" class="list-item">
        <div class="item-label">{{ item.name }}</div>
        <div class="item-value">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import taskResultStore from "@/stores/modules/taskResult";
const taskResult = taskResultStore();
const info = ref([]);
watchEffect(() => {
  info.value = [
    {
      name: "姓名：",
      value: taskResult.taskInfo.patientMapUI?.name,
    },
    {
      name: "性别：",
      value: taskResult.taskInfo.patientMapUI?.gender,
    },
    {
      name: "年龄：",
      value: taskResult.taskInfo.patientMapUI?.age,
    },
    {
      name: "身份证号：",
      value: taskResult.taskInfo.patientMapUI?.idCardNumber,
    },
  ];
});
</script>
