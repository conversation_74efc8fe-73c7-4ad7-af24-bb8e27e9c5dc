<template>
  <el-table
    border
    class="card-table"
    :data="taskResult.taskInfo.formReviewInfo"
    empty-text="暂无数据"
  >
    <el-table-column
      prop="ruleName"
      label="审核项"
      width="200"
    ></el-table-column>
    <el-table-column
      prop="reviewContentInForm"
      label="认定信息"
      width="240"
      :show-overflow-tooltip="{
        effect: 'dark',
        width: '200px' /* 部分版本可能无效 */,
      }"
    ></el-table-column>
    <el-table-column
      prop="mmRuleContent"
      label="规则库-说明书"
      :show-overflow-tooltip="{
        effect: 'dark',
        width: '200px' /* 部分版本可能无效 */,
      }"
    ></el-table-column>
    <el-table-column label="是否匹配" width="120">
      <template #default="scope">
        <div
          v-if="
            scope.row.mmRuleScore != -1 &&
            scope.row.ruleName !== '支付周期限制' &&
            scope.row.ruleName !== '限定支付范围' &&
            scope.row.ruleName !== '所需证明材料'
          "
        >
          <el-tag v-if="scope.row.mmRuleScore === 100" type="success"
            >匹配<el-tooltip
              effect="dark"
              :content="scope.row.mmRuleScoreReason"
              placement="top-start"
              v-if="scope.row.mmRuleScoreReason"
              ><el-icon><InfoFilled /></el-icon></el-tooltip
          ></el-tag>
          <el-tag v-else type="danger"
            >不匹配<el-tooltip
              effect="dark"
              :content="scope.row.mmRuleScoreReason"
              placement="top-start"
              ><el-icon><InfoFilled /></el-icon></el-tooltip
          ></el-tag>
        </div>
      </template>
    </el-table-column>
    <el-table-column
      prop="pmiRuleContent"
      label="规则库-医保"
      :show-overflow-tooltip="{
        effect: 'dark',
        width: '200px' /* 部分版本可能无效 */,
      }"
    ></el-table-column>
    <el-table-column label="是否匹配" width="120">
      <template #default="scope">
        <div v-if="scope.row.pmiRuleScore != -1">
          <el-tag
            v-if="
              scope.row.ruleName == '限定支付范围' ||
              scope.row.ruleName == '所需证明材料'
            "
            type="warning"
            >提示</el-tag
          >
          <div v-else>
            <el-tag v-if="scope.row.pmiRuleScore === 100" type="success"
              >匹配<el-tooltip
                effect="dark"
                :content="scope.row.pmiRuleScoreReason"
                placement="top-start"
                v-if="scope.row.pmiRuleScoreReason"
                ><el-icon><InfoFilled /></el-icon></el-tooltip
            ></el-tag>
            <el-tag v-else type="danger"
              >不匹配<el-tooltip
                effect="dark"
                :content="scope.row.pmiRuleScoreReason"
                placement="top-start"
                ><el-icon><InfoFilled /></el-icon></el-tooltip
            ></el-tag>
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup>
import taskResultStore from "@/stores/modules/taskResult";
const taskResult = computed(() => taskResultStore());
</script>
<style lang="scss" scoped>
:deep(.el-tag__content) {
  display: flex;
  gap: 2px;
  align-items: center;
}</style>
