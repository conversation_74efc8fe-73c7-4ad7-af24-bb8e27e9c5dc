<template>
  <div class="search-info">
    <div class="warning-text">
      注意：若以下无该患者信息，则患者为首次审方，请整理相关资料后重新审方
    </div>
    <div>
      <el-input
        v-model.trim="searchInfo"
        placeholder="请输入身份证号"
        clearable
      ></el-input>
    </div>
    <div>
      <el-button type="primary" @click="searchTableInfo()">检索</el-button>
    </div>
    <div>
      <el-button type="default" @click="searchTableInfo(searchInfo = '')"
        >重置</el-button
      >
    </div>
  </div>
  <el-table border :data="tableData" style="width: 100%">
    <el-table-column width="50">
      <template #default="scope">
        <el-checkbox
          v-model="scope.row.radio"
          @change="handleSelectionChange(scope.row)"
        ></el-checkbox>
      </template>
    </el-table-column>
    <el-table-column prop="name" label="姓名"></el-table-column>
    <el-table-column prop="gender" label="性别"></el-table-column>
    <el-table-column prop="idCardNumber" label="身份证"></el-table-column>
    <el-table-column label="已认定药品">
      <template #default="scope">
        <span v-for="(item, index) in scope.row.listDiagCertForm" :key="index">
          {{ item.mediGeneName }}
        </span>
      </template>
    </el-table-column>
  </el-table>
  <!-- <div class="table-page">
    <el-pagination
      v-model:current-page="page.currentPage"
      v-model:page-size="page.pageSize"
      :page-sizes="[10, 20, 30, 50]"
      size="small"
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="page.total"
      @size-change="sizeChange"
      @current-change="currentChange"
    />
  </div> -->
</template>
<script setup>
import reviewerInfoStore from "@/stores/modules/reviewerInfo";
import commonMethods from "@/hooks/commonMethods";
import api from "@/api";
const { getAgeByIdCard } = commonMethods();
const reviewerInfo = reviewerInfoStore();
const searchInfo = ref("");
const page = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
const tableData = ref([]);
const copyTableData = ref([]);
const handleSelectionChange = (row, col, evn) => {
  tableData.value.forEach((item) => {
    if (item.index != row.index) {
      item.radio = false;
    }
  });
  if (row.radio === true) {
    // 同步赋值处方信息
    reviewerInfo.notFirstPrescriptionForm.name = row.name;
    reviewerInfo.notFirstPrescriptionForm.gender = row.gender;
    reviewerInfo.notFirstPrescriptionForm.age = getAgeByIdCard(
      row.idCardNumber
    );
    reviewerInfo.notFirstPrescriptionForm.idCardNumber = row.idCardNumber;
    reviewerInfo.parentInfo = row;
  } else {
    reviewerInfo.notFirstPrescriptionForm.name = "";
    reviewerInfo.notFirstPrescriptionForm.gender = "";
    reviewerInfo.notFirstPrescriptionForm.age = "";
    reviewerInfo.parentInfo = {};
  }
};
const getTableData = async (val) => {
  let result = await api.patientListApi({
    userName: val ? val : searchInfo.value,
  });
  if (result.errcode === 0) {
    result.detail.forEach((item, index) => {
      item.radio = false;
      item.index = index;
    });
    tableData.value = result.detail;
    copyTableData.value = JSON.parse(JSON.stringify(result.detail));
  }
};
const searchTableInfo = () => {
  if (searchInfo.value) {
    tableData.value = tableData.value.filter((item) => {
      return item.idCardNumber.includes(searchInfo.value);
    });
  } else {
    tableData.value = copyTableData.value;
  }
};
watchEffect(() => {
  getTableData(reviewerInfo.notFirstPrescriptionForm?.name);
});
defineExpose({
  getTableData,
});
// const sizeChange = (val) => {
//   pageSize.value = val;
//   getTableData();
// };
// const currentChange = (val) => {
//   pageNum.value = val;
//   getTableData();
// };
</script>
<style scoped>
.search-info {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}
.warning-text {
  color: red;
  width: 100%;
  flex: 1;
}
.table-page {
  width: 100%;
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}
</style>
