<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <div class="layout-header">
      <top-bar />
    </div>
    
    <!-- 主体内容区域 -->
    <div class="layout-body">
      <!-- 左侧边栏 -->
      <div class="layout-sidebar" v-if="showSidebar">
        <left-bar />
      </div>
      
      <!-- 内容区域 -->
      <div class="layout-content">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container" v-if="showBreadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item 
              v-for="item in breadcrumbList" 
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <!-- 路由视图 -->
        <div class="router-view-container">
          <router-view v-slot="{ Component, route }">
            <transition name="fade-transform" mode="out-in">
              <keep-alive :include="keepAliveComponents">
                <component :is="Component" :key="route.path" />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </div>
    </div>
    
    <!-- 底部信息栏（可选） -->
    <div class="layout-footer" v-if="showFooter">
      <div class="footer-content">
        <p>© 2024 AI审方系统 - 智能药物审查解决方案</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/modules/auth'

const route = useRoute()
const authStore = useAuthStore()

// 布局配置
const showSidebar = ref(true)
const showBreadcrumb = ref(true)
const showFooter = ref(false)

// 需要缓存的组件
const keepAliveComponents = ref(['Home', 'Detail'])

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const breadcrumbs = []
  
  // 添加首页
  if (route.name !== 'home') {
    breadcrumbs.push({
      path: '/home',
      title: '首页'
    })
  }
  
  // 添加当前路由
  matched.forEach(item => {
    if (item.meta.title) {
      breadcrumbs.push({
        path: item.path,
        title: item.meta.title
      })
    }
  })
  
  return breadcrumbs
})

// 根据路由动态调整布局
const updateLayoutConfig = () => {
  const routeName = route.name
  
  switch (routeName) {
    case 'home':
      showSidebar.value = false
      showBreadcrumb.value = false
      break
    case 'detail':
    case 'progress':
      showSidebar.value = true
      showBreadcrumb.value = true
      break
    default:
      showSidebar.value = true
      showBreadcrumb.value = true
  }
}

// 监听路由变化
watch(() => route.name, updateLayoutConfig, { immediate: true })
</script>

<style lang="scss" scoped>
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.layout-header {
  height: 60px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.layout-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 250px;
  background: #fff;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &.collapsed {
    width: 64px;
  }
}

.layout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.breadcrumb-container {
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  
  :deep(.el-breadcrumb) {
    font-size: 14px;
    
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        color: #666;
        
        &:hover {
          color: #409eff;
        }
      }
      
      &:last-child .el-breadcrumb__inner {
        color: #333;
        font-weight: 500;
      }
    }
  }
}

.router-view-container {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.layout-footer {
  height: 50px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .footer-content {
    p {
      margin: 0;
      font-size: 12px;
      color: #999;
    }
  }
}

// 路由过渡动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 响应式设计
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 999;
    transform: translateX(-100%);
    
    &.mobile-show {
      transform: translateX(0);
    }
  }
  
  .layout-content {
    margin-left: 0;
  }
  
  .router-view-container {
    padding: 15px;
  }
}
</style>
