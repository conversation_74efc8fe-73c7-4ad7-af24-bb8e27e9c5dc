/* 右边整体布局样式 开始 */
.right-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .right-top-nav-bar {
    flex-shrink: 0;
    margin-bottom: 16px;
  }
  .right-center-content {
    flex: 1;
    overflow: auto;
    .p-header {
      display: flex;
      gap: 6px;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      margin: 0 12px;
    }
    .card {
      border-radius: 8px;
      background: #fff;
      box-shadow: 0px 0px 16px 0px rgba(21, 102, 80, 0.1);
      padding: 16px;
      margin: 16px 12px;
      .card-line {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid #eee
      }
      // 详情展示
      .card-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 40px;
        .list-item {
          display: flex;
          align-items: center;
          // gap: 10px;
          // .item-value {
          //   color: #666;
          // }
        }
      }
      .card-table {
        margin-top: 16px;
      }
      .error {
        color: #f5222d;
      }
    }
    .card-reverse {
      background: #f6f8fa;
    }
  }
}
/* 结束*/
