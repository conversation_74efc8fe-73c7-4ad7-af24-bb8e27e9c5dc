/* ===== 基础重置 ===== */
* {
  margin: 0;
  box-sizing: border-box;
}
html {
  line-height: 1.15; /* 修复默认行高不一致 */
  -webkit-text-size-adjust: 100%; /* 禁止 iOS 字体缩放 */
}

body {
  min-height: 100vh;
  font-size: 14px;
  color: #333;
  text-rendering: optimizeLegibility; /* 优化文本渲染 */
  overflow: hidden;
}
#app {
  height: 100vh;
  overflow: hidden;
}
/* ===== 列表元素 ===== */
ul, ol {
  list-style: none;
}
/* 修复 Firefox 的 required 样式 */
input:required {
  box-shadow: none;
}

/* ===== 链接与交互元素 ===== */
a {
  color: inherit;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects; /* 移除下划线间隙 */
}
/* ===== 媒体元素 ===== */
img {
  border-style: none; /* 移除 IE10-11 边框 */
  max-width: 100%;
  height: auto;
  vertical-align: middle; /* 消除底部间隙 */
}

svg:not([fill]) {
  fill: currentColor; /* SVG 颜色继承 */
}

/* ===== 表格 ===== */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 滚动条整体部分 */
::-webkit-scrollbar {
    width: 12px;  /* 滚动条的宽度 */
    height: 12px; /* 滚动条的高度 */
}
 
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
    background-color: #888;  /* 滑块颜色 */
    border-radius: 10px;     /* 滑块圆角 */
    border: 2px solid #fff;  /* 滑块边框 */
}
/* 滚动条轨道 */
::-webkit-scrollbar-track {
    background: #f6f8fa;  /* 轨道颜色 */
}
.el-message-box__headerbtn {
  display: none;
}
.el-popper {
  max-width: 40%
}