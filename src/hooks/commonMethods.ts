export default function commonMethods() {
  // 表单验证失败，跳转到错误表单信息行，errorList：表单validate未通过第二个参数，formRef: 表单ref
  const gotoErrorItem = (errorList: any, formRef: any) => {
    Object.keys(errorList).forEach((key, i) => {
      const propName = errorList[key][0].field;
      if (i == 0) {
        formRef.scrollToField(propName);
      }
    });
  };
  // 状态转换
  const statusChange = (status: any) => {
    switch (Number(status)) {
      case 0:
        return '';
      case 1:
        return '资料扫描中';
      case 2:
        return '资料扫描';
      case 3:
        return "OCR识别中";
      case 4:
        return "OCR识别";
      case 5:
        return '患者匹配中';
      case 6:
        return '患者匹配';
      case 7:
        return "审方中";
      case 8:
        return '审方完成';
    }
  };
  // 通过身份证获取年龄
  const getAgeByIdCard = (idCardNumber: any) => {
    if (!idCardNumber) {
      return "";
    }
    // 格式校验
    const reg = /^\d{17}[\dXx]$|^\d{15}$/;
    if (!reg.test(idCardNumber)) {
      console.error("身份证格式错误");
      return null;
    }

    // 处理15位身份证（补全年份前缀）
    if (idCardNumber.length === 15) {
      idCardNumber = idCardNumber.slice(0, 6) + "19" + idCardNumber.slice(6); // 转为18位格式
    }

    // 提取出生日期
    const year = idCardNumber.slice(6, 10);
    const month = idCardNumber.slice(10, 12);
    const day = idCardNumber.slice(12, 14);

    // 创建日期对象
    const birthDate = new Date(`${year}-${month}-${day}`);
    const today = new Date();

    // 计算年龄
    let age = today.getFullYear() - birthDate.getFullYear();
    const currentMonth = today.getMonth();
    const birthMonth = birthDate.getMonth();

    if (
      currentMonth < birthMonth ||
      (currentMonth === birthMonth && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };
  // 通过身份证获取性别
  function getGenderByIdCard(idCardNumber: any) {
    // 1. 格式校验
    const isValid = /^(\d{15}|\d{17}[\dXx])$/.test(idCardNumber);
    if (!isValid) return "身份证格式错误";

    // 2. 处理15位身份证（转换为18位）
    if (idCardNumber.length === 15) {
      idCardNumber = idCardNumber.slice(0, 6) + "19" + idCardNumber.slice(6);
    }

    // 3. 提取性别代码
    const genderCode = idCardNumber.charAt(16);

    // 4. 判断性别
    const code = parseInt(genderCode, 10);
    if (isNaN(code)) return "";
    return code % 2 === 0 ? "女" : "男";
  }
  return { gotoErrorItem, getAgeByIdCard, getGenderByIdCard, statusChange };
}
