import { ElMessageBox } from "element-plus";
import globalStore from "@/stores/modules/global";
import commonMethodsHook from "./commonMethods";
import websocketPrintHook from "./websocketPrint";
import reviewerInfoStore from "@/stores/modules/reviewerInfo";
import api from "@/api";
export default function reviewerCheckHook() {
  const reviewerInfo = reviewerInfoStore();
  const router = useRouter();
  const { gotoErrorItem } = commonMethodsHook();
  const { gotoState, finishTaskFun } = websocketPrintHook();
  const global = globalStore();
  // 模糊过滤药品通用名
  const filterMediGeneName = async (val: any) => {
    let result: any = await api.medicineMatchApi({ mediGeneName: val });
    if (result.errcode === 0) {
      return result.detail.mediGeneName;
    }
  };
  const cancelFunc = (formRef: any) => {
    ElMessageBox.confirm("您确定要结束审方吗？", "提示", {
      confirmButtonText: "结束审方",
      cancelButtonText: "取消",
      type: "warning",
      callback: (info: any) => {
        if (info === "confirm") {
          finishTaskFun();
          global.activeIndex = 0;
          global.patientDialog = false;
          global.currentId = "";
          global.leftList = [];
          reviewerInfo.$reset();
          router.replace({ name: "home" });
          formRef.resetFields();
          // 跟新左侧历史列表，删除掉审核中的数据
        }
      },
    });
  };
  const submitFunc = (formRef: any, formData: any, type: any) => {
    formRef.validate(async (valid: any, errorList: any) => {
      if (valid) {
        console.log(formData);
        // if (type == 1) {
        // if (
        //   formData.patientForm.medicineName !==
        //   formData.prescriptionForm.medicineName
        // ) {
        //   ElMessageBox.confirm("药品信息不匹配，是否结束本次审方？", "提示", {
        //     confirmButtonText: "结束审方",
        //     cancelButtonText: "取消",
        //     type: "warning",
        //     callback: (info: any) => {
        //       if (info === "confirm") {
        //         finishTaskFun();
        //       }
        //     },
        //   });
        //   return false;
        // }
        // }
        if (type == 2) {
          //   if (Object.keys(formData.parentInfo).length === 0) {
          //     ElMessageBox.confirm("未选择有效患者，是否结束本次审方？", "提示", {
          //       confirmButtonText: "结束审方",
          //       cancelButtonText: "取消",
          //       type: "warning",
          //       callback: (info: any) => {
          //         if (info === "confirm") {
          //           finishTaskFun();
          //         }
          //       },
          //     });
          //     return false;
          //   } else {
          //     let state = false;
          //     formData.parentInfo?.medic.forEach((item: any) => {
          //       if (formData.notFirstPrescriptionForm.medicineName === item) {
          //         state = true;
          //       }
          //     });
          //     if (!state) {
          //       ElMessageBox.confirm(
          //         "无" +
          //           formData.notFirstPrescriptionForm.medicineName +
          //           "药品的有效认定信息，本次审方将结束！请完善相关资料后再次审方",
          //         "提示",
          //         {
          //           confirmButtonText: "结束审方",
          //           cancelButtonText: "取消",
          //           type: "warning",
          //           callback: (info: any) => {
          //             if (info === "confirm") {
          //               finishTaskFun();
          //             }
          //           },
          //         }
          //       );
          //       return false;
          //     }
          //   }
        }
        let params: any = {};
        let obj = null;
        params.taskId = global.currentId;
        if (type === 1) {
          obj = formData.patientForm;
          params.diagCertFormMapUI = obj;
          params.prescriptionMapUI = formData.prescriptionForm;
        }
        if (type === 2) {
          obj = formData.notFirstPrescriptionForm;
          params.diagCertFormMapUI = null;
          params.prescriptionMapUI = formData.notFirstPrescriptionForm;
        }
        params.patientMapUI = {
          name: obj.name,
          idCardNumber: obj.idCardNumber,
          gender: obj.gender,
          age: obj.age,
        };
        let result: any = await api.finishTaskApi(params);
        if (result.errcode === 0) {
          global.patientDialog = false;
          formRef.resetFields();
          gotoState({ id: global.currentId });
        } else {
          ElMessageBox.alert(result.chMsg, "提示", {
            confirmButtonText: "结束审方",
            type: "error",
            callback: (info: any) => {
              if (info === "confirm") {
                router.replace({ name: "home" });
              }
            },
          });
        }
      } else {
        // 跳转到错误的输入框
        gotoErrorItem(errorList, formRef);
      }
    });
  };
  return {
    cancelFunc,
    submitFunc,
    filterMediGeneName,
  };
}
