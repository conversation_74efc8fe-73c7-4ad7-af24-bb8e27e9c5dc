import reviewerInfoStore from "@/stores/modules/reviewerInfo";
import api from "@/api";
export default function rulesHook() {
  const mediGeneNameList = ref([]);
  const medicineGoodNameList = ref([]);
  const medicineFactoryList = ref([]);
  const getMedicineList = async () => {
    const { detail, errcode }: any = await api.medicineListApi({});
    if (errcode == 0) {
      let mdg: any = [];
      let mdn: any = [];
      let mf: any = [];
      detail.forEach((item: any) => {
        mdg.push(item.mediGeneName);
        mdn.push({
          label: item.mediTradName,
          value: item.mediTradName,
        });
        mf.push({
          label: item.mediManufacturer,
          value: item.mediManufacturer,
        });
      });
      const uniqueArr:any = [...new Set(mdg)];
      const mdgArr: any = [];
      uniqueArr.forEach((item: any) => {
        mdgArr.push({
          label: item,
          value: item,
        });
      });
      mediGeneNameList.value = mdgArr;
      medicineGoodNameList.value = mdn;
      medicineFactoryList.value = mf;
      return {
        mediGeneNameList: mdgArr,
        medicineGoodNameList: mdn,
        medicineFactoryList: mf,
      };
    }
  };
  const mediRoaOptions = async () => {
    const { detail, errcode }: any = await api.medicineRouteApi({});
    if (errcode == 0) {
      let arr: any = [];
      detail.forEach((item: any) => {
        arr.push({
          label: item.mediRouteCh,
          value: item.mediRouteCh,
        });
      });
      return arr;
    }
  };
  const reviewerInfo = reviewerInfoStore();
  // 校验药品通用名规则
  const editValueValidate = (rule: any, value: any, callback: any) => {
    let state = false;
    mediGeneNameList.value.forEach((item: any) => {
      if (item.label === value) {
        state = true;
      }
    });
    if (state === false) {
      callback(new Error("请选择正确的药品通用名"));
    } else {
      callback();
    }
  };
  // 校验药品商品名规则
  const editGoodValueValidate = (rule: any, value: any, callback: any) => {
    let state = false;
    medicineGoodNameList.value.forEach((item: any) => {
      if (item.label === value) {
        state = true;
      }
    });
    if (state === false) {
      callback(new Error("请选择正确的药品商品名"));
    } else {
      callback();
    }
  };
  // 校验药品厂家规则
  const editFactValueValidate = (rule: any, value: any, callback: any) => {
    let state = false;
    medicineFactoryList.value.forEach((item: any) => {
      if (item.label === value) {
        state = true;
      }
    });
    if (state === false) {
      callback(new Error("请选择正确的药品厂家"));
    } else {
      callback();
    }
  };
  // 只能输入数字
  const ageValidate = (rule: any, value: any, callback: any) => {
    if (value) {
      const reg = /^[0-9]*$/;
      if (!reg.test(value)) {
        callback(new Error("只能输入数字"));
      } else {
        callback();
      }
    }
  };
  // 只能输入汉字和点
  const chineseValidate = (rule: any, value: any, callback: any) => {
    if (value) {
      const reg = /^[\u4E00-\u9FA5.]+$/;
      if (!reg.test(value)) {
        callback(new Error("只能输入汉字"));
      } else {
        callback();
      }
    }
  };
  // 最多输入一位小数
  const decimalValidate = (rule: any, value: any, callback: any) => {
    if (value) {
      // const reg = /^[0-9]+([.]{1}[0-9]{1,2})?$/;
      const reg = /^\d+(\.\d)?$/;
      if (!reg.test(value)) {
        callback(new Error("只能输入数字，最多输入一位小数"));
      } else {
        callback();
      }
    }
  };
  // 身份证校验
  const idCardValidate = (rule: any, value: any, callback: any) => {
    if (value) {
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!reg.test(value)) {
        callback(new Error("身份证格式不正确"));
      } else {
        callback();
      }
    }
  };
  // 联系电话校验
  const phoneValidate = (rule: any, value: any, callback: any) => {
    if (value) {
      const reg = /^1[3-9]\d{9}$/;
      if (!reg.test(value)) {
        callback(new Error("联系电话格式不正确"));
      } else {
        callback();
      }
    } else {
      callback();
    }
  };
  // 表单校验规则
  const rules = {
    // // 患者认定单校验规则
    "patientForm.name": [
      { required: true, message: "姓名不能为空", trigger: "blur" },
      { validator: chineseValidate, trigger: "blur" },
    ],
    "patientForm.gender": [
      { required: true, message: "姓别不能为空", trigger: "change" },
    ],
    "patientForm.age": [
      // { required: true, message: "年龄不能为空", trigger: "blur" },
    ],
    "patientForm.weight": [
      { required: true, message: "体重不能为空", trigger: "blur" },
      { validator: decimalValidate, trigger: "blur" },
    ],
    "patientForm.idCardNumber": [
      { required: true, message: "身份证号不能为空", trigger: "blur" },
      { validator: idCardValidate, trigger: "blur" },
    ],
    "patientForm.certTime": [
      { required: true, message: "认定通过时间不能为空", trigger: "change" },
    ],
    "patientForm.certDisease": [
      { required: true, message: "认定通过病种不能为空", trigger: "blur" },
    ],
    "patientForm.mediGeneName": [
      { required: true, message: "药品通用名不能为空", trigger: "change" },
      { validator: editValueValidate, trigger: "change" },
    ],
    "patientForm.mediTradName": [
      { validator: editGoodValueValidate, trigger: "change" },
    ],
    "patientForm.mediDose": [
      { required: true, message: "剂量不能为空", trigger: "blur" },
    ],
    "patientForm.mediFrequency": [
      { required: true, message: "频次不能为空", trigger: "blur" },
    ],
    "patientForm.mediRoa": [
      { required: true, message: "给药途径不能为空", trigger: "change" },
    ],
    "patientForm.phone": [
      // { required: true, message: "联系电话不能为空", trigger: "blur" },
      { validator: phoneValidate, trigger: "blur" },
    ],

    // 处方字段校验规则
    "prescriptionForm.presTime": [
      { required: true, message: "处方日期不能为空", trigger: "change" },
    ],
    "prescriptionForm.certDisease": [
      { required: true, message: "诊断不能为空", trigger: "blur" },
    ],
    "prescriptionForm.mediGeneName": [
      { required: true, message: "药品通用名不能为空", trigger: "change" },
      { validator: editValueValidate, trigger: "change" },
    ],
    "prescriptionForm.mediTradName": [
      // { required: true, message: "药品商品名不能为空", trigger: "change" },
      { validator: editGoodValueValidate, trigger: "change" },
    ],
    "prescriptionForm.mediManufacturer": [
      { required: true, message: "药品厂家不能为空", trigger: "change" },
      { validator: editFactValueValidate, trigger: "change" },
    ],
    "prescriptionForm.mediDose": [
      { required: true, message: "剂量不能为空", trigger: "blur" },
    ],
    "prescriptionForm.mediFrequency": [
      { required: true, message: "频次不能为空", trigger: "blur" },
    ],
    "prescriptionForm.mediRoa": [
      { required: true, message: "给药途径不能为空", trigger: "change" },
    ],
    // 非首次处方字段校验规则

    "notFirstPrescriptionForm.name": [
      { required: true, message: "姓名不能为空", trigger: "blur", disabled: true },
      { validator: chineseValidate, trigger: "blur" },
    ],
    "notFirstPrescriptionForm.gender": [
      { required: true, message: "姓别不能为空", trigger: "change" },
    ],
    "notFirstPrescriptionForm.age": [
      { required: true, message: "年龄不能为空", trigger: "blur" },
    ],
    "notFirstPrescriptionForm.idCardNumber": [
      { required: true, message: "身份证号不能为空", trigger: "blur" },
      { validator: idCardValidate, trigger: "blur" },
    ],
    "notFirstPrescriptionForm.presTime": [
      { required: true, message: "处方日期不能为空", trigger: "change" },
    ],
    "notFirstPrescriptionForm.certDisease": [
      { required: true, message: "诊断不能为空", trigger: "blur" },
    ],
    "notFirstPrescriptionForm.mediGeneName": [
      { required: true, message: "药品通用名不能为空", trigger: "change" },
      { validator: editValueValidate, trigger: "blur" },
    ],
    "notFirstPrescriptionForm.mediTradName": [
      // { required: true, message: "药品商品名不能为空", trigger: "change" },
      { validator: editGoodValueValidate, trigger: "change" },
    ],
    "notFirstPrescriptionForm.mediManufacturer": [
      { required: true, message: "药品厂家不能为空", trigger: "change" },
      { validator: editFactValueValidate, trigger: "change" },
    ],
    "notFirstPrescriptionForm.mediDose": [
      { required: true, message: "剂量不能为空", trigger: "blur" },
    ],
    "notFirstPrescriptionForm.mediFrequency": [
      { required: true, message: "频次不能为空", trigger: "blur" },
    ],
    "notFirstPrescriptionForm.mediRoa": [
      { required: true, message: "给药途径不能为空", trigger: "change" },
    ],
  };
  const columnsForPatient = ref<any>([]);
  const columnsForPrescription = ref<any>([]);
  const noFirstColumnsForPrescription = ref<any>([]);
  onMounted(async () => {
    const result: any = await getMedicineList();
    const mediRoaOptionsResult: any = await mediRoaOptions();
    // 患者认定表信息
    columnsForPatient.value = [
      {
        prop: "name",
        label: "姓名",
        placeholder: "请输入姓名",
        type: "input",
        maxlength: 32,
      },
      {
        prop: "gender",
        label: "性别",
        type: "radio",
        options: [
          { label: "男", value: "男" },
          { label: "女", value: "女" },
        ],
      },
      { prop: "age", label: "年龄", type: "input", placeholder: "请输入年龄" },
      {
        prop: "weight",
        label: "体重",
        placeholder: "请输入体重",
        type: "input",
        unit: "kg",
      },
      {
        prop: "idCardNumber",
        label: "身份证号",
        placeholder: "请输入身份证号",
        type: "input",
      },
      // {
      //   prop: "address",
      //   label: "患者参保地",
      //   placeholder: "请输入患者参保地",
      //   type: "input",
      //   maxlength: 64,
      // },
      {
        prop: "certAuthority",
        label: "认定机构",
        placeholder: "请输入认定机构",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "certPhysician",
        label: "认定医生",
        placeholder: "请输入认定医生",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "certTime",
        label: "认定通过时间",
        placeholder: "",
        type: "date",
      },
      {
        prop: "certDisease",
        label: "认定通过病种",
        placeholder: "请输入认定通过病种",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediGeneName",
        label: "药品通用名",
        placeholder: "请选择药品通用名",
        type: "inputAndselect",
        filterable: true,
        disabled: true,
        options: result.mediGeneNameList,
      },
      {
        prop: "mediTradName",
        label: "药品商品名",
        placeholder: "请输入药品商品名",
        type: "select",
        filterable: true,
        options: result.medicineGoodNameList,
      },
      {
        prop: "mediDose",
        label: "剂量",
        placeholder: "请输入剂量",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediFrequency",
        label: "频次",
        placeholder: "请输入频次",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediRoa",
        label: "给药途径",
        placeholder: "请输入给药途径",
        type: "select",
        options: mediRoaOptionsResult,
      },
      {
        prop: "phone",
        label: "联系电话",
        placeholder: "请输入联系电话",
        type: "input",
        maxlength: 11,
      },
    ];
    // 处方信息
    columnsForPrescription.value = [
      {
        prop: "presTime",
        label: "处方日期",
        placeholder: "请选择处方日期",
        type: "date",
      },
      {
        prop: "certDisease",
        label: "诊断",
        placeholder: "请输入诊断",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediGeneName",
        label: "药品通用名",
        placeholder: "请选择药品通用名",
        type: "inputAndselect",
        filterable: true,
        disabled: true,
        options: result.mediGeneNameList,
      },
      {
        prop: "mediTradName",
        label: "药品商品名",
        placeholder: "请输入药品商品名",
        type: "select",
        filterable: true,
        options: result.medicineGoodNameList,
      },
      {
        prop: "mediManufacturer",
        label: "药品厂家",
        placeholder: "请选择药品厂家",
        type: "select",
        filterable: true,
        options: result.medicineFactoryList,
      },
      {
        prop: "mediDose",
        label: "剂量",
        placeholder: "请输入剂量",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediFrequency",
        label: "频次",
        placeholder: "请输入频次",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediRoa",
        label: "给药途径",
        placeholder: "请输入给药途径",
        type: "select",
        options: mediRoaOptionsResult,
      },
    ];
    noFirstColumnsForPrescription.value = [
      {
        prop: "name",
        label: "姓名",
        placeholder: "请输入姓名",
        type: "inputAdnBtn",
        maxlength: 32,
        disabled: true,
        btnText: "修改",
      },
      {
        prop: "idCardNumber",
        label: "身份证号",
        placeholder: "请输入身份证号",
        type: "input",
      },
      {
        prop: "gender",
        label: "性别",
        type: "radio",
        options: [
          { label: "男", value: "男" },
          { label: "女", value: "女" },
        ],
      },
      {
        prop: "age",
        label: "年龄",
        type: "input",
        placeholder: "请输入年龄",
      },
      {
        prop: "presTime",
        label: "处方日期",
        placeholder: "请选择处方日期",
        type: "date",
      },
      {
        prop: "certDisease",
        label: "诊断",
        placeholder: "请输入诊断",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediGeneName",
        label: "药品通用名",
        placeholder: "请选择药品通用名",
        type: "inputAndselect",
        filterable: true,
        disabled: true,
        filterMethod: true,
        options: result.mediGeneNameList,
      },
      {
        prop: "mediTradName",
        label: "药品商品名",
        placeholder: "请选择药品商品名",
        type: "select",
        filterable: true,
        filterMethod: true,
        options: result.medicineGoodNameList,
      },
      {
        prop: "mediManufacturer",
        label: "药品厂家",
        placeholder: "请选择药品厂家",
        type: "select",
        filterable: true,
        filterMethod: true,
        options: result.medicineFactoryList,
      },
      {
        prop: "mediDose",
        label: "剂量",
        placeholder: "请输入剂量",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediFrequency",
        label: "频次",
        placeholder: "请输入频次",
        type: "input",
        maxlength: 64,
      },
      {
        prop: "mediRoa",
        label: "给药途径",
        placeholder: "请输入给药途径",
        type: "select",
        options: mediRoaOptionsResult,
        filterable: true,
      },
    ];
  });

  return {
    rules,
    columnsForPatient,
    columnsForPrescription,
    noFirstColumnsForPrescription,
  };
}
