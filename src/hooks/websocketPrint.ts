import globalStore from "@/stores/modules/global";
import { ElMessageBox } from "element-plus";
import api from "@/api";
import reviewerInfoStore from "@/stores/modules/reviewerInfo";
import taskResultStore from "@/stores/modules/taskResult";
export default function websocketPrintHook() {
  const router = useRouter();
  const global = globalStore();
  const reviewerInfo = reviewerInfoStore();
  const taskResult = taskResultStore();
  var timer: any = ref(null);
  const stopTimer = () => {
    if (timer.value !== null) {
      clearInterval(timer.value);
      timer.value = null; // 重置引用，避免内存泄漏或其他问题
    }
  };
  const oneKeyCheck = async () => {
    // 先检查是否有其他任务正在执行。
    const result: any = await api.getBusyTaskApi({});
    if (result.errcode === 0) {
      if (result.detail.isBusy) {
        // 控制状态为6之前的任务
        ElMessageBox.confirm(
          result.chMsg ? result.chMsg : "有其他任务正在进行",
          "提示",
          {
            confirmButtonText: "查看",
            cancelButtonText: "结束审方",
            showCancelButton: true,
            type: "error",
            callback: (info: any) => {
              if (info === "confirm") {
                router.replace({
                  name: "progress",
                  params: { id: result.detail.taskId },
                });
              }
              if (info === "cancel") {
                // 结束审方
                finishTaskFun(result.detail.taskId);
                router.replace({ name: "home" });
              }
            },
          }
        );
        return false;
      } else {
        progressFun();
      }
    } else {
      ElMessage.error(result.chMsg);
      return;
    }
  };
  const progressFun = async () => {
    const { errcode, chMsg, detail }: any = await api.startscanApi({});
    if (errcode != 0) {
      stopTimer();
      ElMessageBox.alert(chMsg, "提示", {
        confirmButtonText: "结束审方",
        type: "error",
        callback: (info: any) => {
          if (info === "confirm") {
            router.replace({ name: "home" });
            prtaskExecutor();
          }
        },
      });
    } else {
      global.$reset();
      reviewerInfo.$reset();
      taskResult.$reset();
      global.currentId = detail.taskId;
      // 跳转到查看进度的界面
      router.push({ name: "progress", params: { id: detail.taskId } });
      gotoState(detail.taskId);
    }
  };
  const gotoState = async (id: any) => {
    stopTimer();
    // 获取正在处理的任务id
    let res: any = await api.getTaskResultApi({
      taskId: global.currentId,
    });
    console.log((res.detail, "正常调用接口返回值"));
    if (res.errcode === 0 || res.errcode === 2003) {
      if (res.detail.isFinish) {
        stopTimer();
        return false;
      }
      global.activeIndex = res.detail.status;
      global.description = res.detail.description;
      if (
        res.detail.status < 4 ||
        res.detail.status == 6 ||
        res.detail.status == 7
      ) {
        timer.value = window.setInterval(async () => {
          let { errcode, detail, chMsg }: any = await api.getTaskResultApi({
            taskId: res.detail.taskId,
          });
          console.log(detail, "轮询接口返回值");
          if (errcode === 0 || errcode === 2003) {
            if (detail.isFinish || detail.status == null) {
              console.log("isFinish = true");
              stopTimer();
              return false;
            }
            global.activeIndex = detail.status;
            global.description = detail.description;
            // 通过接口，跟新leftBar。
            if (detail.status == 4 || detail.status == 8) {
              stopTimer();
              if (detail.status == 8) {
                taskResult.taskInfo = detail;
                router.replace({
                  name: "detail",
                  params: { id: res.detail.taskId },
                });
              }
              if (detail.status == 4) {
                getFormInfo({ errcode: errcode, detail: detail, chMsg: chMsg });
              }
            }
          } else {
            stopTimer();
            ElMessageBox.alert(chMsg, "提示", {
              confirmButtonText: "结束审方",
              type: "error",
              callback: (info: any) => {
                if (info === "confirm") {
                  router.replace({ name: "home" });
                  prtaskExecutor();
                }
              },
            });
          }
        }, 3000);
      } else {
        stopTimer();
        if (res.detail.status == 4) {
          getFormInfo(res);
        }
      }
    } else {
      stopTimer();
      ElMessageBox.alert(res.chMsg, "提示", {
        confirmButtonText: "结束审方",
        type: "error",
        callback: (info: any) => {
          if (info === "confirm") {
            router.replace({ name: "home" });
            prtaskExecutor();
          }
        },
      });
    }
  };
  const getFormInfo = (res: any) => {
    global.patientDialog = true;
    // 给弹框复制
    if (res.detail.diagCertFormMapUI === null) {
      // 非初次
      global.firstCheckState = false;
      let arr = Object.keys(reviewerInfo.notFirstPrescriptionForm);
      if (
        res.detail.prescriptionMapUI &&
        Object.keys(res.detail.prescriptionMapUI).length > 0
      ) {
        let diagArr = Object.keys(res.detail.prescriptionMapUI);
        diagArr.forEach((key) => {
          if (arr.indexOf(key) !== -1) {
            reviewerInfo.notFirstPrescriptionForm[key] =
              res.detail.prescriptionMapUI[key];
          }
        });
      }
      if (
        res.detail.patientMapUI &&
        Object.keys(res.detail.patientMapUI).length > 0
      ) {
        let diagArr = Object.keys(res.detail.patientMapUI);
        diagArr.forEach((key) => {
          if (arr.indexOf(key) !== -1) {
            reviewerInfo.notFirstPrescriptionForm[key] =
              res.detail.patientMapUI[key];
          }
        });

        diagArr.forEach((key) => {
          if (arr.indexOf(key) !== -1) {
            reviewerInfo.patientForm[key] = res.detail.patientMapUI[key];
          }
        });
      }
    } else {
      // 初次
      global.firstCheckState = true;
      let arr = Object.keys(reviewerInfo.patientForm);
      // 组合处认定表信息
      if (
        res.detail.diagCertFormMapUI &&
        Object.keys(res.detail.diagCertFormMapUI).length > 0
      ) {
        let diagArr = Object.keys(res.detail.diagCertFormMapUI);
        diagArr.forEach((key) => {
          if (arr.indexOf(key) !== -1) {
            reviewerInfo.patientForm[key] = res.detail.diagCertFormMapUI[key];
          }
        });
      } else {
        reviewerInfo.patientForm = {}; // 为空不显示
      }
      if (
        res.detail.patientMapUI &&
        Object.keys(res.detail.patientMapUI).length > 0
      ) {
        let diagArr = Object.keys(res.detail.patientMapUI);
        diagArr.forEach((key) => {
          if (arr.indexOf(key) !== -1) {
            reviewerInfo.patientForm[key] = res.detail.patientMapUI[key];
          }
        });
      }
      // 组合处方信息
      const keys: String[] = Object.keys(reviewerInfo.prescriptionForm);
      if (
        res.detail.prescriptionMapUI &&
        Object.keys(res.detail.prescriptionMapUI).length > 0
      ) {
        let diagArr = Object.keys(res.detail.prescriptionMapUI);
        diagArr.forEach((key) => {
          if (keys.indexOf(key) !== -1) {
            reviewerInfo.prescriptionForm[key] =
              res.detail.prescriptionMapUI[key];
          }
        });
      } else {
        reviewerInfo.prescriptionForm = {}; // 为空不显示
      }
    }
  };
  const finishTaskFun = async (taskId?: string) => {
    stopTimer();
    const { errcode, detail, chMsg }: any = await api.abnormalFinishTaskApi({
      taskId: global.currentId ? global.currentId : taskId,
      description: "用户手动结束审方",
    });
    if (errcode == 0) {
      await prtaskExecutor();
      global.currentId = "";
      global.activeIndex = 0;
      global.description = "";
    } else {
      ElMessage.error(chMsg);
    }
  };
  // 修改流程状态
  const editStatusFun = async (state: any, bool: any) => {
    let result: any = await api.updateStatusApi({
      taskId: global.currentId,
      status: state,
    });
    if (result.errcode === 0) {
      router.push({ name: "progress", params: { id: global.currentId } });
      if (bool) {
        gotoState(global.currentId);
      }
    } else {
      ElMessage.error(result.chMsg);
    }
  };
  // 查询左侧列表
  const prtaskExecutor = async () => {
    let result: any = await api.prtaskExecutorApi({
      executor: "executor",
      recentHours: 24,
      limit: 50,
    });
    if (result.errcode === 0) {
      global.leftList = result.detail;
      return result.detail;
    } else {
      ElMessage.error(result.chMsg);
    }
  };
  // 查詢結果信息
  const getTaskResultFun = async () => {
    let result: any = await api.getTaskResultApi({taskId: global.currentId})
    if (result.errcode === 0) {
      return result.detail;
    }
  }
  return {
    oneKeyCheck,
    gotoState,
    progressFun,
    finishTaskFun,
    editStatusFun,
    prtaskExecutor,
    getTaskResultFun,
  };
}
