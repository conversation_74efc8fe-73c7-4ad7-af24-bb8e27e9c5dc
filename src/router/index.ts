import { createRouter, createWebHashHistory } from "vue-router";
import { useAuthStore } from "@/stores/modules/auth";
import { ElMessage } from "element-plus";
const routes = [
  {
    path: "/login",
    name: "login",
    meta: {
      title: "登录 - AI审方系统",
      keepAlive: false,
    },
    component: () => import("@/views/Login.vue"),
  },
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/main",
    name: "main",
    component: () => import("@/views/MainLayout.vue"),
    children: [
      {
        path: "/",
        redirect: "/home",
      },
      {
        path: "/home",
        name: "home",
        meta: {
          title: "AI审方",
          keepAlive: false,
        },
        component: () => import("@/views/Home.vue"),
      },
      {
        path: "/detail/:id",
        name: "detail",
        meta: {
          title: "审方结果",
          keepAlive: false,
        },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHashHistory("/"),
  routes,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore();

  // 不需要登录的页面
  const publicPages = ["/login"];
  const isPublicPage = publicPages.includes(to.path);

  // 如果是公开页面，直接通过
  if (isPublicPage) {
    next();
    return;
  }

  // 检查是否已登录
  if (!authStore.isLoggedIn) {
    ElMessage.warning("请先登录");
    next("/login");
    return;
  }

  // 检查token是否有效
  if (!authStore.checkTokenValid()) {
    ElMessage.warning("登录已过期，请重新登录");
    authStore.logout();
    next("/login");
    return;
  }

  next();
});
export default router;
