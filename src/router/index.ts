import { createRouter, createWebHashHistory } from "vue-router";
const routes = [
  {
    path: "/",
    name: "home",
    meta: {
      title: "AI审方",
      keepAlive: false,
    },
    component: () => import("@/views/Home.vue"),
  },
  {
    path: "/detail/:id",
    name: "detail",
    meta: {
      title: "审方结果",
      keepAlive: false,
    },
    component: () => import("@/views/Detail.vue"),
  },
  {
    path: "/progress/:id",
    name: "progress",
    meta: {
      title: "审方进度",
      keepAlive: false,
    },
    component: () => import("@/views/Progress.vue"),
  },
  // {
  //   //实现路由重定向，当进入网页时，路由自动跳转到路由
  //   redirect: "/viewPage",
  //   path: "/",
  // },
];

const router = createRouter({
  history: createWebHashHistory("/"),
  routes,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});
router.beforeEach((to, from, next) => {
  // global.routerName = from.name
  next();
});
export default router;
