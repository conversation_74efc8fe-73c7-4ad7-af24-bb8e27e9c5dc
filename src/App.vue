<template>
  <div class="main">
    <!-- elementPlus组件汉化 -->
    <el-config-provider :locale="localeZH">
      <router-view v-slot="{ Component }">
        <keep-alive>
          <component
            :is="Component"
            :key="$route.fullPath"
            v-if="$route.meta.keepAlive"
          />
        </keep-alive>
        <component
          :is="Component"
          :key="$route.fullPath"
          v-if="!$route.meta.keepAlive"
        />
      </router-view>
    </el-config-provider>
  </div>
</template>
<script setup>
import localeZH from "element-plus/es/locale/lang/zh-cn";
</script>
<style scoped lang="scss">
.main {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  height: 100%;
  overflow: hidden;
  flex-shrink: 0;
  background: #f6f8fa;
}

:deep(.el-dialog) {
  margin-top: 8vh;
}

:deep(.el-table__header) {
  th.el-table__cell {
    background: #f1f1f1;
    color: #333;
  }
}
</style>
