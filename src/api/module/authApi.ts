import request from "@/utils/request";

// 登录接口参数类型
export interface LoginParams {
  username: string;
  password: string;
  captcha: string;
}

// 登录响应类型
export interface LoginResponse {
  code: number;
  message: string;
  data: {
    token: string;
    userInfo: {
      id: string;
      username: string;
      name: string;
      role: string;
    };
  };
}

// 用户登录
export const login = (params: LoginParams): Promise<LoginResponse> => {
  return request({
    url: "/v0-1/auth/login",
    method: "post",
    data: params,
  });
};

// 用户登出
export const logout = (): Promise<any> => {
  return request({
    url: "/v0-1/auth/logout",
    method: "post",
  });
};

// 获取用户信息
export const getUserInfo = (): Promise<any> => {
  return request({
    url: "/v0-1/auth/userinfo",
    method: "get",
  });
};

// 刷新token
export const refreshToken = (): Promise<any> => {
  return request({
    url: "/v0-1/auth/refresh",
    method: "post",
  });
};
