import request from "@/utils/request";
import requestWeb from "@/utils/requestWeb";
/******************************************************************************* */
// 查看任务是否繁忙
export const getBusyTaskApi = (params: any) => {
  return requestWeb({
    url: "/v0-1/workstation/get-busy-task",
    method: "get",
    params: params,
  });
};

// 获取进度条结果
export const getTaskResultApi = (data: any) => {
  return requestWeb({
    url: "/v0-1/workstation/get-task-result",
    method: "post",
    data: data,
  });
};
// 开始扫描文件
export const startscanApi = (data: any) => {
  return requestWeb({
    url: "/v0-1/workstation/startscan",
    method: "post",
    data: data,
  });
};
// 手动确认认定表和处方信息
export const finishTaskApi = (data: any) => {
  return request({
    url: "/v0-1/prtask/finish-check",
    method: "post",
    data: data,
  });
};
// 修改审方状态
export const updateStatusApi = (data: any) => {
  return request({
    url: "/v0-1/prtask/update-status",
    method: "post",
    data: data,
  });
};
// 查询病人列表
export const patientListApi = (data: any) => {
  return request({
    url: "/v0-1/prtask/prapp/patient-list",
    method: "post",
    data: data,
  });
};
// 结束审方
export const abnormalFinishTaskApi = (data: any) => {
  return requestWeb({
    url: "/v0-1/workstation/abnormal-finish-task",
    method: "post",
    data: data,
  });
};
// 结束审方
export const medicineListApi = (params: any) => {
  return request({
    url: "/v0-1/prtask/prapp/medicine-list",
    method: "get",
    params,
  });
};
export const medicineRouteApi = (params: any) => {
  return request({
    url: "/v0-1/prtask/prapp/medi-route",
    method: "get",
    params,
  });
};
// 通过orc药品通用名模糊匹配一个最接近的药品名称
export const medicineMatchApi = (params: any) => {
  return request({
    url: "/v0-1/prtask/prapp/medicine-match",
    method: "get",
    params,
  });
};
// 查看资料ai审方结果数据
export const taskMaterialApi = (params: any) => {
  return request({
    url: "/v0-1/prtask/prapp/task-material",
    method: "get",
    params,
  });
};
export const updateTaskMaterialApi = (data: any) => {
  return request({
    url: "/v0-1/prtask/prapp/update-task-material",
    method: "post",
    data: data,
  });
};
// 获取左侧菜单数据
export const prtaskExecutorApi = (params: any) => {
  return request({
    url: `/v0-1/prtask/executor/${params.executor}`,
    method: "get",
    params,
  });
};
// 确认是否匹配
export const updateReviewInfoApi = (data: any) => {
  return request({
    url: "/v0-1/prtask/update-review-info",
    method: "post",
    data: data,
  });
};